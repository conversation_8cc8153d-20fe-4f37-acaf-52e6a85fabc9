/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:53:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-20 15:24:30
 */
import $http from "@/utils/httpRequest";

export default {
	// 获取当前用户信息
	getUserInfo: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/users/currentUser");
			resolve(response);
		});
	},
	// 获取登录日志列表
	get_LoginLogsList: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/events", param);
			resolve(response);
		});
	},

	// 获取所有的用户
	get_allUsers: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/users", param);
			resolve(response);
		});
	},

	// 获取所有的二级用户组
	get_allGroups: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/groups", param);
			resolve(response);
		});
	},

	// 获取用户组的后代组
	get_GroupsChild: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get(`/UAMS/groups/${param.groupId}/offspring`);
			resolve(response);
		});
	},

	// 获取登录率
	get_LoginLogsPercent: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/statistical/login_rat", param);
			resolve(response);
		});
	},

	// aims日志记录列表
	get_AIMSLogsData: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/AIMS/log/histories/list", param);
			resolve(response);
		});
	},

	// 获取scenator所有的场景
	get_AllScenes: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/Scenator/userScenes", param);
			resolve(response);
		});
	},

	// 获取访问日志数据
	get_VisitedData: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/ClickLog/getClickLogs", param);
			resolve(response);
		});
	},

	// 获取访问率数据
	get_VisitedPerData: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/ClickLog/getClickStatistics", param);
			resolve(response);
		});
	},

	// 访问记录导出
	get_ExportXsl: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/ClickLog/exportClickLogs", param, {
				responseType: "blob",
			});
			resolve(response);
		});
	},

	// 获取当前用户
	getCurrentUser: function (param) {
		return new Promise((resolve, reject) => {
			let response = $http.get("/UAMS/users/currentUser", param);
			resolve(response);
		});
	},
};
