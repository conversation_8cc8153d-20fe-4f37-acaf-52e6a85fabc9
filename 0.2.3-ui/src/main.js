/*
 * @Descriptin: 
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:53:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-05 14:08:30
 */
import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "./assets/reset.css";
import * as echarts from "echarts";
import VueI18n from "vue-i18n";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import enLocale from "element-ui/lib/locale/lang/en";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
import ruLocale from "element-ui/lib/locale/lang/ru-RU";
import en_US from "./languages/en_US.json";
import zh_CN from "./languages/zh_CN.json";
import ru_RU from "./languages/ru_RU.json";


Vue.config.productionTip = false;

Vue.use(VueI18n);

const locale = localStorage.getItem("lang");

const i18n = new VueI18n({
	locale: locale ? locale : "zh_CN", // 语言标识 //this.$i18n.locale // 通过切换locale的值来实现语言切换
	messages: {
		zh_CN: Object.assign(zh_CN, zhLocale),
		en_US: Object.assign(en_US, enLocale),
		ru_RU: Object.assign(ru_RU, ruLocale),
	},
	silentTranslationWarn: true,
});

Vue.use(ElementUI, { size: "small", i18n: (key, value) => i18n.t(key, value), });

Vue.prototype.$echarts = echarts;

new Vue({
	i18n,
	router,
	store,
	render: (h) => h(App),
}).$mount("#app");
