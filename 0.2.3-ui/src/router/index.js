import Vue from 'vue'
import VueRouter from 'vue-router'

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter)

const routes = [
  {
    path: "/",
    redirect: "/controlLogs"
  },
  {
    path: '/',
    name: 'Layout',
    component: (resolve)=> require(["@/views/Layout"], resolve),
    children: [
      {
        path: '/controlLogs',
        name: 'controlLogs',
        component: (resolve)=> require(["@/views/ControlLogs"], resolve)
      },
      {
        path: '/loginPercent',
        name: 'loginPercent',
        component: (resolve)=> require(["@/components/loginPercent/LoginPercent"], resolve)
      },
      {
        path: '/visitedPercent',
        name: 'visitedPercent',
        component: (resolve)=> require(["@/components/visitedPercent/VisitPerCard"], resolve)
      }
    ]
  },
]

const router = new VueRouter({
  mode: 'hash',
  routes
})

export default router
