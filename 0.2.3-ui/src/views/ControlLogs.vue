<!--
 * @Descriptin: 
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-03-22 10:33:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-05 14:23:28
-->
<template>
	<div class="control-logs">
		<el-tabs
			class="scenatorStyle"
			type="card"
			v-model="activeName"
			@tab-click="handleClick"
		>
			<el-tab-pane
				v-for="(item, idx) in tabList"
				:key="idx"
				:label="item.label"
				:title="item.label"
				:name="item.name"
			>
				<component
					:is="currentCard"
					v-if="currentCard === item.name"
				></component>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import LoginCard from "@/components/controlLogs/ContrlCard";
import ChangeCard from "@/components/controlLogs/ChangeCard";
import VisitCard from "@/components/controlLogs/VisitCard";
export default {
	components: {
		LoginCard,
		ChangeCard,
		VisitCard,
	},
	data() {
		return {
			activeName: "LoginCard",
			tabList: [
				{
					label: this.$t("zh_CN.ControlLogs.tab_list.tab_1"),
					name: "LoginCard",
				},
				{
					label: this.$t("zh_CN.ControlLogs.tab_list.tab_2"),
					name: "ChangeCard",
				},
				{
					label: this.$t("zh_CN.ControlLogs.tab_list.tab_3"),
					name: "VisitCard",
				},
			],
			currentCard: "LoginCard",
		};
	},
	methods: {
		handleClick(val) {
			this.currentCard = val.name;
		},
	},
};
</script>

<style lang="less" scoped>
.control-logs {
	height: 100%;
}
/deep/.el-tabs {
	height: 100%;
	.el-tabs__nav-scroll {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}
	.el-tabs__content {
		height: 100%;
		.el-tab-pane {
			height: 100%;
		}
	}
}
</style>
