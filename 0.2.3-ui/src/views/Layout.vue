<template>
	<div class="layout">
		<main>
			<div class="slidMenu">
				<el-menu
					:default-active="defaultActive"
					class="el-menu-vertical-demo"
					@select="selectItem"
					:collapse="isCollapse"
				>
					<el-submenu
						v-for="item in menuList"
						:key="item.index"
						:index="item.index"
					>
						<template slot="title" v-if="item.title">
							<i style="margin: 0 5px 0 -5px">
								<svg
									v-if="item.svg"
									width="16px"
									height="16px"
									viewBox="0 0 16 16"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									xmlns:xlink="http://www.w3.org/1999/xlink"
								>
									<title>Icon/操作日志 2</title>
									<g
										id="图标"
										stroke="none"
										stroke-width="1"
										fill="none"
										fill-rule="evenodd"
									>
										<g
											transform="translate(-228.000000, -144.000000)"
											id="Icon/操作日志-2"
										>
											<g
												transform="translate(228.000000, 144.000000)"
												id="编组"
											>
												<polygon
													id="button-[9-9]"
													transform="translate(8.000000, 8.000000) scale(-1, -1) translate(-8.000000, -8.000000) "
													points="0 0 16 0 16 16 0 16"
												></polygon>
												<g id="文件-word_file-word" fill-rule="nonzero">
													<rect
														id="矩形"
														fill-opacity="0.01"
														fill="#FFFFFF"
														x="0"
														y="0"
														width="16"
														height="16"
													></rect>
													<polygon
														id="路径"
														fill-opacity="0.01"
														fill="#FFFFFF"
														points="16 0 0 0 0 16 16 16"
													></polygon>
													<path
														d="M14,13 L14,14.3 C14,14.68661 13.6642138,15 13.25,15 L13.25,15 L2.75,15 C2.335775,15 2,14.68661 2,14.3 L2,14.3 L2,13 L3,13 L3,14 L13,14 L13,13 L14,13 Z M13.25,1 C13.6642138,1 14,1.3134005 14,1.7 L14,1.7 L14,6 L13,6 L13,2 L6.145,2 L3,4.935 L3,6 L2,6 L2,4.5 L5.75,1 Z"
														id="形状结合"
														fill="#0854A1"
													></path>
													<path
														d="M14,6 C14.5522847,6 15,6.44771525 15,7 L15,12 C15,12.5522847 14.5522847,13 14,13 L2,13 C1.44771525,13 1,12.5522847 1,12 L1,7 C1,6.44771525 1.44771525,6 2,6 L14,6 Z M14,12 L14,7 L2,7 L2,12 L14,12 Z"
														id="矩形"
														fill="#0854A1"
													></path>
													<g
														id="LOG"
														transform="translate(2.984226, 7.608407)"
														fill="#0854A1"
													>
														<polygon
															id="路径"
															points="0 0.109863281 0.751953125 0.109863281 0.751953125 3.06152344 2.53417969 3.06152344 2.53417969 3.70849609 0 3.70849609"
														></polygon>
														<path
															d="M4.64599609,3.17138672 C4.94384766,3.17138672 5.18025716,3.06152344 5.35522461,2.84179688 C5.53019206,2.62207031 5.61767578,2.30957031 5.61767578,1.90429688 C5.61767578,1.50065104 5.53019206,1.18855794 5.35522461,0.968017578 C5.18025716,0.747477214 4.94384766,0.637207031 4.64599609,0.637207031 C4.34814453,0.637207031 4.11051432,0.747070313 3.93310547,0.966796875 C3.75569661,1.18652344 3.66699219,1.49902344 3.66699219,1.90429688 C3.66699219,2.30957031 3.75569661,2.62207031 3.93310547,2.84179688 C4.11051432,3.06152344 4.34814453,3.17138672 4.64599609,3.17138672 Z M6.36962891,1.90429688 C6.36962891,2.55045573 6.18815104,3.0452474 5.82519531,3.38867188 C5.55338542,3.66861979 5.16031901,3.80859375 4.64599609,3.80859375 C4.13167318,3.80859375 3.73860677,3.66861979 3.46679688,3.38867188 C3.10221354,3.0452474 2.91992188,2.55045573 2.91992188,1.90429688 C2.91992188,1.24511719 3.10221354,0.750325521 3.46679688,0.419921875 C3.73860677,0.139973958 4.13167318,0 4.64599609,0 C5.16031901,0 5.55338542,0.139973958 5.82519531,0.419921875 C6.18815104,0.750325521 6.36962891,1.24511719 6.36962891,1.90429688 Z"
															id="形状"
														></path>
														<path
															d="M9.36279297,1.23291016 C9.30582682,0.987141927 9.16666667,0.815429688 8.9453125,0.717773438 C8.82161458,0.6640625 8.68408203,0.637207031 8.53271484,0.637207031 C8.2430013,0.637207031 8.00496419,0.746663411 7.81860352,0.965576172 C7.63224284,1.18448893 7.5390625,1.51367188 7.5390625,1.953125 C7.5390625,2.39583333 7.63997396,2.70914714 7.84179687,2.89306641 C8.04361979,3.07698568 8.27311198,3.16894531 8.53027344,3.16894531 C8.78255208,3.16894531 8.98925781,3.09611003 9.15039062,2.95043945 C9.31152344,2.80476888 9.41080729,2.61393229 9.44824219,2.37792969 L8.61572266,2.37792969 L8.61572266,1.77734375 L10.1147461,1.77734375 L10.1147461,3.70849609 L9.61669922,3.70849609 L9.54101562,3.25927734 C9.39615885,3.43017578 9.26595052,3.55061849 9.15039062,3.62060547 C8.95182292,3.74267578 8.70768229,3.80371094 8.41796875,3.80371094 C7.94108073,3.80371094 7.55045573,3.63850911 7.24609375,3.30810547 C6.92871094,2.97607422 6.77001953,2.52197266 6.77001953,1.94580078 C6.77001953,1.36311849 6.93033854,0.895996094 7.25097656,0.544433594 C7.57161458,0.192871094 7.99560547,0.0170898437 8.52294922,0.0170898437 C8.98030599,0.0170898437 9.34773763,0.133056641 9.62524414,0.364990234 C9.90275065,0.596923828 10.061849,0.886230469 10.1025391,1.23291016 L9.36279297,1.23291016 Z"
															id="路径"
														></path>
													</g>
												</g>
											</g>
										</g>
									</g>
								</svg>
							</i>
							<i v-if="item.icon" :class="item.icon" style="color: #000"></i>
							<span slot="title">{{ item.title }}</span>
						</template>
						<el-menu-item
							v-for="ele in item.menuItem"
							:key="ele.index"
							:index="ele.index"
							>{{ ele.name }}</el-menu-item
						>
					</el-submenu>
				</el-menu>
				<div class="collapse">
					<i
						:class="[
							!isCollapse ? 'el-icon-s-fold' : 'el-icon-s-unfold',
							'icon-font',
						]"
						@click="collapse"
					></i>
				</div>
			</div>
			<div class="rightContent" id="rightContent">
				<router-view></router-view>
			</div>
		</main>
	</div>
</template>

<script>
export default {
	data() {
		return {
			isCollapse: false,
			defaultActive: "1-1",
			menuList: [
				{
					index: "1",
					title: this.$t("zh_CN.Layout.menu_nav.menu_main_title"),
					svg: true,
					menuItem: [
						{
							index: "1-1",
							name: this.$t("zh_CN.Layout.menu_nav.menu_item_list.item_1"),
							path: "/controlLogs",
						},
						{
							index: "1-2",
							name: this.$t("zh_CN.Layout.menu_nav.menu_item_list.item_2"),
							path: "/loginPercent",
						},
						{
							index: "1-3",
							name: this.$t("zh_CN.Layout.menu_nav.menu_item_list.item_3"),
							path: "/visitedPercent",
						},
					],
				},
			],
		};
	},
	watch: {
		$route: {
			handler: function (val, oldVal) {
				this.menuList.forEach((_item) => {
					_item.menuItem &&
						_item.menuItem.forEach((_i) => {
							if (_i.path === val.fullPath) {
								console.log(_i.index);
								this.defaultActive = _i.index;
							}
						});
				});
			},
			deep: true,
		},
	},
	mounted() {
		if (window.history && window.history.pushState) {
			window.addEventListener("popstate", this.goBack, false);
		}
		if (this.$route.path != "") {
			// 这里只针对一层结构简单的的做个路由跟踪，如果层级较深，应更换为递归遍历的形式
			this.menuList[0].menuItem.forEach((item) => {
				if (item.path === this.$route.path) {
					this.defaultActive = item.index;
				}
			});
		}
	},
	onBeforeUnmount() {
		window.removeEventListener("popstate", this.goBack, false);
	},
	methods: {
		goBack() {
			if (window.location.hash) {
				this.$forceUpdate();
				let path = window.location.hash.split("#")[1];
				this.menuList[0].menuItem.forEach((item) => {
					if (item.path === path) {
						this.defaultActive = item.index;
					}
				});
			}
		},
		// 左侧菜单栏展开关闭
		collapse() {
			this.isCollapse = !this.isCollapse;
			const bodyDom = document.getElementById("rightContent");
			if (this.isCollapse) {
				bodyDom.style.width = "100%";
			} else {
				bodyDom.style.width = "calc(100% - 201px)";
			}
		},
		selectItem(index) {
			let idxArr = index.split("-");
			let result = this.indexFind(idxArr[0], idxArr, this.menuList);
			this.$router.push({
				path: result.path,
			});
		},
		indexFind(idx, idxArr, arr) {
			let target = null;
			for (let ele of arr) {
				if (ele.index && ele.index === idx) {
					let remaining = idxArr.splice(1);
					if (remaining.length > 0) {
						idx = idx + "-" + remaining[0];
						return this.indexFind(idx, remaining, ele.menuItem);
					} else {
						target = ele;
						return target;
					}
				} else {
					target = null;
				}
			}
		},
	},
};
</script>

<style lang="less" scoped>
.layout {
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}
main {
	flex: 1;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	position: relative;
	.slidMenu {
		display: flex;
		flex-direction: column;
		align-items: center;
		border-right: 1px solid #d9d9d9;
		// width: 201px;
		::v-deep.el-submenu__title {
			height: 42px;
			line-height: 42px;
		}
		::v-deep.el-menu-vertical-demo:not(.el-menu--collapse) {
			width: 201px;
			min-height: 201px;
		}
		::v-deep.el-submenu__title:focus {
			background: transparent;
		}
		::v-deep.el-submenu__title:hover {
			background: #ecf5ff;
		}
		.el-menu {
			border: none;
			width: 100%;
		}
		.el-menu-item:focus,
		.el-menu-item:hover {
			// background: #c4c9cf;
			background: none;
			color: #000;
		}
		.el-menu-item {
			&:hover {
				background: #e5f0fa;
			}
		}
		.el-menu-item.is-active {
			box-sizing: border-box;
			color: #0854a1;
			background: #e5f0fa;
			border-bottom: 2px solid #0854a1;
		}
		.el-icon-s-claim {
			&::before {
				color: #0854a1;
			}
		}
		.el-icon-s-fold {
			&::before {
				color: #0854a1;
			}
		}
		.el-icon-s-unfold {
			&::before {
				color: #0854a1;
			}
		}
	}
	.collapse {
		height: 44px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		bottom: 0;
		.icon-font {
			cursor: pointer;
		}
	}
	.rightContent {
		width: calc(100% - 201px);
	}
}
</style>
