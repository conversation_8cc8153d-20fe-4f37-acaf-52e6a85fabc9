<template>
	<div class="conCard">
		<div class="conHeader">
			<div class="filterList">
				<el-form ref="form" :label-position="labelPosition" :model="form">
					<div class="filter-group">
						<el-form-item prop="controler">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_1') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_1") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.controler"
								clearable
								reserve-keyword
								@visible-change="
									(e) => {
										keyValueChange(e, 6);
									}
								"
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_1')"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="controlerSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 6);
											}
										"
										:placeholder="$t('zh_CN.ChangeCard.placeholder.place_8')"
									></el-input>
								</div>
								<el-option
									v-for="ele in controlerOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="secondorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_2') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_2") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="secondorgName"
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_2')"
								@change="selectSecondName"
								@clear="clearTag"
								clearable
								@visible-change="
									(e) => {
										keyValueChange(e, 2);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="secondSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 2);
											}
										"
										:placeholder="$t('zh_CN.ChangeCard.placeholder.place_8')"
									></el-input>
								</div>
								<el-option
									v-for="ele in secondOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="thridorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_3') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_3") + ":" }}
							</div>
							<el-tooltip
								class="item"
								effect="light"
								v-if="form.secondorgName === ''"
								:content="$t('zh_CN.ChangeCard.placeholder.place_5')"
								placement="top-end"
							>
								<el-select
									class="scenatorStyle"
									popper-class="scenator_selectPopper controlLogs_selectPopper"
									:disabled="form.secondorgName !== '' ? false : true"
									v-model="form.thridorgName"
									:placeholder="$t('zh_CN.ChangeCard.placeholder.place_2')"
									clearable
									style="width: 100%"
								>
									<el-option
										v-for="ele in thirdOptions"
										:key="ele.id"
										:label="ele.name"
										:value="ele.name"
									>
									</el-option>
								</el-select>
							</el-tooltip>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-if="form.secondorgName !== ''"
								:disabled="form.secondorgName !== '' ? false : true"
								v-model="form.thridorgName"
								clearable
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_2')"
								@visible-change="
									(e) => {
										keyValueChange(e, 3);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="thirdSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 3);
											}
										"
										:placeholder="$t('zh_CN.ChangeCard.placeholder.place_8')"
									></el-input>
								</div>
								<el-option
									v-for="ele in thirdOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
					</div>
					<div class="filter-group">
						<el-form-item prop="deviceName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_4') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_4") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.deviceName"
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_3')"
								clearable
								@visible-change="
									(e) => {
										keyValueChange(e, 4);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="deviceSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 4);
											}
										"
										:placeholder="$t('zh_CN.ChangeCard.placeholder.place_8')"
									></el-input>
								</div>
								<el-option
									v-for="ele in deviceOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="changeContent">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_5') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_5") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.changeContent"
								clearable
								@visible-change="
									(e) => {
										keyValueChange(e, 5);
									}
								"
								reserve-keyword
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_4')"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="changeContentSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 5);
											}
										"
										:placeholder="$t('zh_CN.ChangeCard.placeholder.place_8')"
									></el-input>
								</div>
								<el-option
									v-for="ele in changeContentOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<div
								class="form-item-label"
								:title="$t('zh_CN.ChangeCard.form_labels.label_6') + ':'"
							>
								{{ $t("zh_CN.ChangeCard.form_labels.label_6") + ":" }}
							</div>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48.5%"
								v-model="form.startTime"
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_6')"
								:picker-options="picker_disabled"
								value-format="yyyy-MM-dd"
								@change="changeTime(true)"
							>
							</el-date-picker>
							<span class="time-splite">—</span>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48.5%"
								:placeholder="$t('zh_CN.ChangeCard.placeholder.place_7')"
								:picker-options="picker_disabled"
								v-model="form.endTime"
								value-format="yyyy-MM-dd"
								@change="changeTime(false)"
							>
							</el-date-picker>
						</el-form-item>
					</div>
					<div class="filter-group">
						<div class="filterBtn">
							<el-button
								type="secondary--button"
								class="scenatorStyle"
								plain
								@click="filterTable"
								>{{ $t("zh_CN.ChangeCard.btn_1") }}</el-button
							>
							<el-button
								type="secondary--button"
								class="scenatorStyle"
								plain
								@click="initData('form')"
								>{{ $t("zh_CN.ChangeCard.btn_2") }}</el-button
							>
							<el-button
								type="main--button"
								class="scenatorStyle"
								plain
								@click="exportXls"
								:disabled="tableData.length > 0 ? false : true"
								>{{ $t("zh_CN.ChangeCard.btn_3") }}</el-button
							>
						</div>
					</div>
				</el-form>
			</div>
		</div>
		<div class="conBody scenatorStyle">
			<el-table
				id="out-table"
				:data="tableData"
				border
				stripe
				:default-sort="{ prop: 'gmtCreated', order: 'descending' }"
				:show-overflow-tooltip="true"
				:max-height="tableHeight"
			>
				<el-table-column
					v-for="(item, idx) in columns"
					:key="idx"
					:prop="item.prop"
					:label="item.label"
					:width="item.width"
					:sortable="item.sortable"
					:sort-method="item.sort_method"
					align="center"
				>
				</el-table-column>
				<template slot="empty">
					<div class="empty">
						<img src="@/assets/empty.png" alt="暂无数据！" />
						<span>{{ $t("zh_CN.ChangeCard.empty") }}</span>
					</div>
				</template>
			</el-table>
		</div>
		<!-- <div class="conFoot">
            <el-pagination
                :current-page.sync="currentPage"
                :page-sizes="[30, 40, 50, 60]"
                :page-size="pageSize"
                layout="sizes, prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <i class="el-icon-refresh-right refresh"></i>
            <span class="pageTips">当前显示从1到{{pageSize}}页共{{totalCount}}条数据</span>
        </div> -->
	</div>
</template>

<script>
import { mapActions } from "vuex";
import FilterData from "../filterData";
import FileSaver from "file-saver";
import XLSX from "xlsx";
import { getSpell } from "../../utils/jian-pinyin";
import GroupFilter from "../../utils/groupFilter.js";
const groupFilter = new GroupFilter();
export default {
	name: "ContrlCard",
	data() {
		return {
			controlNameSourceData: [],
			controlNameOptions: [],

			controlerSourceData: [],
			controlerOptions: [],

			secondSourceData: [],
			secondOptions: [],

			thirdSourceData: [],
			thirdOptions: [],

			deviceSourceData: [],
			deviceOptions: [],

			changeContentSourceData: [],
			changeContentOptions: [],
			picker_disabled: {
				disabledDate: (time) => {
					return time.getTime() > Date.now();
				},
			},
			secondorgName: "",
			form: {
				controler: "",
				secondorgName: "",
				thridorgName: "",
				deviceName: "",
				changeContent: "",
				startTime: "",
				endTime: "",
			},
			columns: [
				{
					prop: "createdBy",
					label: this.$t("zh_CN.ChangeCard.form_labels.label_1"),
					width: 160,
					sortable: true,
					sort_method: (a, b) => {
						if (a.createdBy === "") a.createdBy = " ";
						if (b.createdBy === "") b.createdBy = " ";
						return a.createdBy.localeCompare(b.createdBy);
					},
				},
				{
					prop: "levelTwoOrganization",
					label: this.$t("zh_CN.ChangeCard.form_labels.label_2"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.levelTwoOrganization === "") a.levelTwoOrganization = " ";
						if (b.levelTwoOrganization === "") b.levelTwoOrganization = " ";
						return a.levelTwoOrganization.localeCompare(b.levelTwoOrganization);
					},
				},
				{
					prop: "levelThreeOrganization",
					label: this.$t("zh_CN.ChangeCard.form_labels.label_3"),
					sortable: true,
					sort_method: (a, b) => {
						if (!a.levelThreeOrganization) a.levelThreeOrganization = " ";
						if (!b.levelThreeOrganization) b.levelThreeOrganization = " ";
						return a.levelThreeOrganization.localeCompare(
							b.levelThreeOrganization
						);
					},
				},
				{
					prop: "device",
					label: this.$t("zh_CN.ChangeCard.form_labels.label_4"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.device === "") a.device = " ";
						if (b.device === "") b.device = " ";
						return a.device.localeCompare(b.device);
					},
				},
				{
					prop: "operationDescription",
					label: this.$t("zh_CN.ChangeCard.form_labels.label_5"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.operationDescription === "") a.operationDescription = " ";
						if (b.operationDescription === "") b.operationDescription = " ";
						return a.operationDescription.localeCompare(b.operationDescription);
					},
				},
				{
					prop: "gmtCreated",
					label: this.$t("zh_CN.ChangeCard.operation_time"),
					sortable: true,
				},
				{
					prop: "ip",
					label: this.$t("zh_CN.ChangeCard.ip_address"),
				},
			],
			tableData: [],
			fold: true,

			totalCount: 100,
			pageSize: 30,
			currentPage: 1,

			factory: null,
			keyValue: "",

			labelPosition: "left",

			tableHeight: window.innerHeight - 240,
		};
	},
	mounted() {
		this.fetchDate();
		this.getAllUsers();
		this.getTwoGroups();
		// this.get_allLogsData()
		this.$nextTick(() => {
			window.onresize = () => {
				this.tableHeight = window.innerHeight - 240;
			};
		});
	},
	methods: {
		...mapActions([
			"get_AIMSLogsData",
			// 获取所有的用户组
			"get_allGroups",
			"get_allUsers",
		]),

		// 获取二级用户组
		getTwoGroups() {
			this.get_allGroups().then((res) => {
				if (res && res instanceof Array) {
					this.allgroups = res;
					groupFilter.initData(res);
					this.secondOptions = groupFilter.getNodesByTreeLeval(2);
					this.secondSourceData = groupFilter.getNodesByTreeLeval(2);
				}
			});
		},

		// 获取所有用户
		getAllUsers() {
			this.get_allUsers().then((res) => {
				if (res && res instanceof Array) {
					let sortArr = [];
					res.forEach((item) => {
						if (item.name) {
							sortArr.push({
								soreCode: this.getStringCode(getSpell(item.name)),
								...item,
							});
						}
					});
					sortArr.sort((a, b) => {
						return a.soreCode.charCodeAt() - b.soreCode.charCodeAt();
					});
					this.controlerOptions = sortArr;
					this.controlerSourceData = sortArr;
					this.get_allLogsData();
				}
			});
		},
		// 获取名字首字母，并转成大写字母
		getStringCode(str) {
			const strArr = str && str.split(",");
			let strOne = strArr[0];
			if (strOne.substr(0, 1) === "[") {
				return strOne.substr(1, 1).toUpperCase();
			} else {
				return strOne.substr(0, 1).toUpperCase();
			}
		},
		// 选择二级用户组获取三级用户组
		selectSecondName(e) {
			this.form.thridorgName = "";
			this.secondSourceData.forEach((so) => {
				if (so.id === this.secondorgName) {
					this.form.secondorgName = so.name;
				}
			});
			this.thirdOptions = groupFilter.getAllChildNodesById(e);
			this.thirdSourceData = groupFilter.getAllChildNodesById(e);
		},

		// 筛选条件清空
		initData(e) {
			this.secondorgName = ""
			this.$refs[e].resetFields();
			this.fetchDate();
			this.getAllUsers();
		},

		// 下拉框开启关闭时的数据状态
		keyValueChange(flag, tag) {
			if (!flag) {
				this.keyValue = "";
			} else {
				switch (tag) {
					case 1:
						this.controlNameOptions = this.controlNameSourceData;
						break;
					case 2:
						this.secondOptions = this.secondSourceData;
						break;
					case 3:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 4:
						this.deviceOptions = this.deviceSourceData;
						break;
					case 5:
						this.changeContentOptions = this.changeContentSourceData;
						break;
					case 6:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},

		dropDownSearch(e, tag) {
			if (this.keyValue !== "") {
				switch (tag) {
					case 1:
						this.controlNameOptions = this.controlNameSourceData.filter(
							(ele) => {
								if (ele.name.includes(this.keyValue)) {
									return ele;
								}
							}
						);
						break;
					case 2:
						this.secondOptions = this.secondSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 3:
						this.thirdOptions = this.thirdSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 4:
						this.deviceOptions = this.deviceSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 5:
						this.changeContentOptions = this.changeContentSourceData.filter(
							(ele) => {
								if (ele.name.includes(this.keyValue)) {
									return ele;
								}
							}
						);
						break;
					case 6:
						this.controlerOptions = this.controlerSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					default:
						return;
				}
			} else {
				switch (tag) {
					case 1:
						this.controlNameOptions = this.controlNameSourceData;
						break;
					case 2:
						this.secondOptions = this.secondSourceData;
						break;
					case 3:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 4:
						this.deviceOptions = this.deviceSourceData;
						break;
					case 5:
						this.changeContentOptions = this.changeContentSourceData;
						break;
					case 6:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},
		// 获取全部的数据
		get_allLogsData() {
			let _this = this;
			this.get_AIMSLogsData().then(async function (res) {
				if (res) {
					res.forEach((item) => {
						_this.controlerSourceData.forEach((ele) => {
							if (item.createdBy === ele.username) {
								item.createdBy = ele.name;
							}
						});
					});
					_this.tableData = res;
					// 初始化数据过滤工厂
					_this.factory = new FilterData({
						tableData: _this.tableData,
					});
					let {
						controlList = [],
						controlerList = [],
						secondOrgNameList = [],
						deviceNameList = [],
						changeContentList = [],
					} = await _this.factory.get_FilterListOptions();
					_this.controlNameOptions = controlList;
					_this.controlNameSourceData = controlList;

					// _this.controlerOptions = controlerList
					// _this.controlerSourceData = controlerList

					// _this.secondOptions = secondOrgNameList
					// _this.secondSourceData = secondOrgNameList

					_this.deviceOptions = deviceNameList;
					_this.deviceSourceData = deviceNameList;

					_this.changeContentOptions = changeContentList;
					_this.changeContentSourceData = changeContentList;
				}
			});
		},

		// 条件筛选数据
		filterTable() {
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			this.form.startTime = this.form.startTime
				? this.form.startTime + " " + "00:00:01"
				: "";
			this.form.endTime = this.form.startTime
				? this.form.endTime + " " + "23:59:59"
				: "";
			let param = this.form;
			this.factory.get_filterTabledata(param).then((res) => {
				this.tableData = res;
			});
		},
		// 选择二级用户组获取三级用户组
		// async selectSecondName(e) {
		//     if(e !== '') {
		//         let { thirdList } = await this.factory.filter_thirdOrgName(e)
		//         this.thirdSourceData = thirdList
		//         this.thirdOptions = this.thirdSourceData
		//     }
		// },
		// 清空二级用户组的同时清空三级用户组选项
		clearTag() {
			this.thirdOptions = [];
			this.form.thridorgName = "";
		},
		// 选择时间范围
		changeTime(flag) {
			// flag为true时代表起始时间的选择
			// flag为false时代表结束时间的选择
			if (flag) {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			} else {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			}
		},
		// 校验选择的时间是否符合规范
		checkTime(flag, start, end) {
			if (flag) {
				if (new Date(start) > new Date(end)) {
					this.form.startTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "开始时间大于结束时间",
					});
				}
			} else {
				if (new Date(start) > new Date(end)) {
					this.form.endTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "结束时间小于开始时间",
					});
				}
			}
		},
		// 判断时间条件是否完整
		timeIsFull() {
			if (this.form.startTime && this.form.endTime) {
				return true;
			} else if (this.form.startTime && !this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "结束时间不能为空",
				});
				return false;
			} else if (!this.form.startTime && this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "开始时间不能为空",
				});
				return false;
			} else {
				return true;
			}
		},
		changeStatus() {
			this.fold = !this.fold;
		},
		// 设置默认日期
		async fetchDate() {
			var now = new Date();
			var year = now.getFullYear();
			var month = now.getMonth() + 1;
			var day = now.getDate();
			var hour = now.getHours();
			var min = now.getMinutes();
			var seconds = now.getSeconds();
			const after =
				year +
				"-" +
				(month < 10 ? "0" + month : month) +
				"-" +
				(day < 10 ? "0" + day : day);
			const before = await this.getBeforeDate(Date.now());

			this.form.startTime = before;
			this.form.endTime = after;
		},

		// 获取当前日期30天前的日期
		getBeforeDate(temp) {
			return new Promise((resove, reject) => {
				var timestr = new Date(temp - 60 * 60 * 24 * 30 * 1000);

				var year = timestr.getFullYear();

				var month = timestr.getMonth() + 1;

				var date = timestr.getDate();

				var hour = timestr.getHours();

				var minute = timestr.getMinutes();

				var second = timestr.getSeconds();

				var datetime =
					year +
					"-" +
					(month < 10 ? "0" + month : month) +
					"-" +
					(date < 10 ? "0" + date : date);

				resove(datetime);
			});
		},

		// 导出按钮
		exportXls() {
			this.exportExcel();
		},
		//定义导出Excel表格事件
		exportExcel() {
			/* 从表生成工作簿对象 */
			var wb = XLSX.utils.table_to_book(document.querySelector("#out-table"), {
				raw: true,
			});
			/* 获取二进制字符串作为输出 */
			var wbout = XLSX.write(wb, {
				bookType: "xlsx",
				bookSST: true,
				type: "array",
			});
			try {
				FileSaver.saveAs(
					//Blob 对象表示一个不可变、原始数据的类文件对象。
					//Blob 表示的不一定是JavaScript原生格式的数据。
					//File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
					//返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
					new Blob([wbout], { type: "application/octet-stream" }),
					//设置导出文件名称
					"变更日志.xlsx"
				);
			} catch (e) {
				if (typeof console !== "undefined") console.log(e, wbout);
			}
			return wbout;
		},
	},
};
</script>

<style lang="less" scoped>
.conCard {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 0 20px;
	.conHeader {
		background: #fff;
		display: flex;
		flex-direction: row;
		.filterList {
			flex: 1;
			overflow: hidden;
		}
		.el-form {
			flex: 1;
			.el-form-item {
				width: 31%;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				align-items: center;
				.form-item-label {
					min-width: 100px;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}
			}
		}
		::v-deep.el-form-item__label {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		/deep/.el-form-item__content {
			width: 100%;
			line-height: 30px;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		.filter-group {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			flex-wrap: nowrap;
		}
		.filterBtn {
			margin-left: auto;
			height: 50px;
			display: flex;
			align-items: center;
		}
	}
	.conBody {
		width: 100%;
		.empty {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			img {
				width: 322px;
				height: 224px;
			}
		}
		/deep/.el-table {
			height: 100%;
			.el-table__body-wrapper {
				.el-table__empty-block {
					height: calc(100vh - 292px) !important;
				}
			}
			.cell {
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}
	}
	.conFoot {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 40px;
		background: #f7f7f7;
		padding: 0 10px;
		.refresh {
			cursor: pointer;
			animation: run 1s infinite;
		}
		.refresh_active {
			animation: run 2s infinite;
		}
		@keyframes run {
			0% {
				transform: rotateZ(0deg);
			}
			100% {
				transform: rotateZ(360deg);
			}
		}
		.pageTips {
			font-size: 14px;
			margin-left: auto;
		}
	}
}
</style>
