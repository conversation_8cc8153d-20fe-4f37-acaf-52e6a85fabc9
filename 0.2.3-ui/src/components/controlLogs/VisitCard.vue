<template>
	<div class="visitCard">
		<div class="conHeader">
			<div class="filterList">
				<el-form ref="form" :label-position="labelPosition" :model="form">
					<div class="filter-group">
						<el-form-item prop="controler">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitCard.form_labels.label_1') + ':'"
							>
								{{ $t("zh_CN.VisitCard.form_labels.label_1") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.controler"
								clearable
								reserve-keyword
								:disabled="controleable"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_1')"
								@visible-change="
									(e) => {
										keyValueChange(e, 4);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="controlerSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 4);
											}
										"
										:placeholder="$t('zh_CN.VisitCard.placeholder.place_7')"
									></el-input>
								</div>
								<el-option
									v-for="ele in controlerOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="secondorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitCard.form_labels.label_2') + ':'"
							>
								{{ $t("zh_CN.VisitCard.form_labels.label_2") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.secondorgName"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_2')"
								clearable
								@change="secoundChange"
								@clear="clearTag"
								@visible-change="
									(e) => {
										keyValueChange(e, 1);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="secondSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 1);
											}
										"
										:placeholder="$t('zh_CN.VisitCard.placeholder.place_7')"
									></el-input>
								</div>
								<el-option
									v-for="ele in secondOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="thridorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitCard.form_labels.label_3') + ':'"
							>
								{{ $t("zh_CN.VisitCard.form_labels.label_3") + ":" }}
							</div>
							<el-tooltip
								class="item"
								effect="light"
								v-if="form.secondorgName === ''"
								:content="$t('zh_CN.VisitCard.placeholder.place_4')"
								placement="top-end"
							>
								<el-select
									class="scenatorStyle"
									popper-class="scenator_selectPopper controlLogs_selectPopper"
									:disabled="form.secondorgName !== '' ? false : true"
									v-model="form.thridorgName"
									:placeholder="$t('zh_CN.VisitCard.placeholder.place_2')"
									clearable
									style="width: 100%"
								>
									<el-option
										v-for="ele in thirdOptions"
										:key="ele.id"
										:label="ele.name"
										:value="ele.id"
									>
									</el-option>
								</el-select>
							</el-tooltip>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-if="form.secondorgName !== ''"
								:disabled="form.secondorgName !== '' ? false : true"
								v-model="form.thridorgName"
								clearable
								@visible-change="
									(e) => {
										keyValueChange(e, 2);
									}
								"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_2')"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="thirdSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 2);
											}
										"
										:placeholder="$t('zh_CN.VisitCard.placeholder.place_7')"
									></el-input>
								</div>
								<el-option
									v-for="ele in thirdOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
					</div>
					<div class="filter-group">
						<el-form-item prop="visitContent">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitCard.form_labels.label_4') + ':'"
							>
								{{ $t("zh_CN.VisitCard.form_labels.label_4") + ":" }}
							</div>
							<el-input
								size="small"
								style="width: 100%"
								v-model="form.visitContent"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_7')"
							></el-input>
						</el-form-item>
						<el-form-item>
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitCard.form_labels.label_5') + ':'"
							>
								{{ $t("zh_CN.VisitCard.form_labels.label_5") + ":" }}
							</div>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48.5%"
								v-model="form.startTime"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_5')"
								:picker-options="picker_disabled"
								value-format="yyyy-MM-dd"
								@change="changeTime(true)"
							>
							</el-date-picker>
							<span class="time-splite">—</span>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48.5%"
								:placeholder="$t('zh_CN.VisitCard.placeholder.place_6')"
								:picker-options="picker_disabled"
								v-model="form.endTime"
								value-format="yyyy-MM-dd"
								@change="changeTime(false)"
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item>
							<div class="filterBtn">
								<el-button
									plain
									@click="searchData"
									type="secondary--button"
									class="scenatorStyle"
									>{{ $t("zh_CN.VisitCard.btn_1") }}</el-button
								>
								<el-button
									plain
									@click="initData('form')"
									type="secondary--button"
									class="scenatorStyle"
									>{{ $t("zh_CN.VisitCard.btn_2") }}</el-button
								>
								<el-button
									plain
									class="scenatorStyle"
									type="main--button"
									:disabled="tableData.length > 0 ? false : true"
									@click="exportXls"
									>{{ $t("zh_CN.VisitCard.btn_3") }}</el-button
								>
							</div>
						</el-form-item>
					</div>
				</el-form>
			</div>
		</div>
		<div class="conBody">
			<el-table
				ref="myTable"
				:data="tableData"
				border
				stripe
				:default-sort="{ prop: 'createTime', order: 'descending' }"
				:max-height="tableHeight"
				id="out-table"
			>
				<template v-for="(item, idx) in columns">
					<el-table-column
						:key="idx"
						:prop="item.prop"
						:label="item.label"
						:sortable="item.sortable"
						:sort-method="item.sort_method"
						align="center"
						v-if="item.prop != 'logBehavior'"
						show-overflow-tooltip
					>
					</el-table-column>
				</template>
				<template slot="empty">
					<div class="empty">
						<img src="@/assets/empty.png" alt="暂无数据！" />
						<span>{{ $t("zh_CN.VisitCard.empty") }}</span>
					</div>
				</template>
			</el-table>
		</div>
		<!-- <div class="conFoot">
            <el-pagination
                :current-page.sync="currentPage"
                @current-change="chagePage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pageSize"
                @size-change="chageSize"
                layout="sizes, prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <span class="pageTips">当前显示从1到{{Math.ceil(totalCount/pageSize)}}页共{{totalCount}}条数据</span>
        </div> -->
	</div>
</template>

<script>
import { mapActions } from "vuex";
import FileSaver from "file-saver";
import XLSX from "xlsx";
import GroupFilter from "../../utils/groupFilter.js";
import { getSpell } from "../../utils/jian-pinyin";
const groupFilter = new GroupFilter();
export default {
	name: "VisitCard",
	data() {
		return {
			labelPosition: "left",
			controlerSourceData: [],
			controlerOptions: [],

			secondSourceData: [],
			secondOptions: [],

			thirdSourceData: [],
			thirdOptions: [],

			visitSourceData: [],
			visitContentOptions: [],
			picker_disabled: {
				disabledDate: (time) => {
					return time.getTime() > Date.now();
				},
			},
			form: {
				controler: "",
				secondorgName: "",
				thridorgName: "",
				visitContent: "",
				startTime: "",
				endTime: "",
			},
			visitToSearch: "",
			tableData: [],
			columns: [
				{
					prop: "userName",
					label: this.$t("zh_CN.VisitCard.form_labels.label_1"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.userName === "") a.userName = " ";
						if (b.userName === "") b.userName = " ";
						return a.userName.localeCompare(b.userName);
					},
				},
				{
					prop: "groupName",
					label: this.$t("zh_CN.VisitCard.form_labels.label_2"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.groupName === "") a.groupName = " ";
						if (b.groupName === "") b.groupName = " ";
						return a.groupName.localeCompare(b.groupName);
					},
				},
				{
					prop: "thirdGroup",
					label: this.$t("zh_CN.VisitCard.form_labels.label_3"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.thirdGroup === "") a.thirdGroup = " ";
						if (b.thirdGroup === "") b.thirdGroup = " ";
						return a.thirdGroup.localeCompare(b.thirdGroup);
					},
				},
				{
					prop: "logContent",
					label: this.$t("zh_CN.VisitCard.form_labels.label_4"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.logContent === "") a.logContent = " ";
						if (b.logContent === "") b.logContent = " ";
						return a.logContent.localeCompare(b.logContent);
					},
				},
				{
					prop: "createTime",
					label: this.$t("zh_CN.VisitCard.operation_time"),
					sortable: true,
				},
				{
					prop: "ip",
					label: this.$t("zh_CN.VisitCard.ip_address"),
				},
				{
					prop: "logBehavior",
					label: this.$t("zh_CN.VisitCard.visitor_logBehavior"),
					sortable: true,
				}
			],

			secondGroupOrder: [],
			thirdGroupOrder: [],
			currentPage: 1,
			pageSize: 20,
			totalCount: 0,
			keyValue: "",
			allgroups: [],

			controleable: false,

			tableHeight: window.innerHeight - 208,
		};
	},
	mounted() {
		this.fetchDate();
		this.getAllUsers();
		this.getTwoGroups();
		// this.getAllScenes();
		this.tableListener();
		this.$nextTick(() => {
			window.onresize = () => {
				this.tableHeight = window.innerHeight - 208;
			};
		});
	},
	methods: {
		...mapActions([
			"get_AllScenes", // 获取所有的场景名称
			"get_allUsers", // 获取所有的用户
			"get_allGroups", // 获取所有的用户组
			"get_VisitedData", //获取登录日志数据
			"get_ExportXsl", //导出访问记录数据文件
			// 获取当前用户
			"getCurrentUser",
		]),

		// 获取当前用户
		getUser() {
			return new Promise((resolve) => {
				this.getCurrentUser()
					.then((res) => {
						let isSysGroup = [];
						if (res.groups.length > 0) {
							isSysGroup = res.groups.filter((group) => {
								if (group.sysGroup === true) {
									return group;
								}
							});
						}
						if (isSysGroup.length > 0) {
							resolve(true);
						} else {
							resolve(res);
						}
					})
					.catch((error) => {
						resolve(true);
					});
			});
		},

		// 筛选条件清空
		initData(e) {
			this.$refs[e].resetFields();
			this.currentPage = 1;
			this.tableData = [];
			this.fetchDate();
			this.getAllUsers();
		},
		// 下拉框开启关闭时的数据状态
		keyValueChange(flag, tag) {
			if (!flag) {
				this.keyValue = "";
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 3:
						this.visitContentOptions = this.visitSourceData;
						break;
					case 4:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},
		dropDownSearch(e, tag) {
			if (this.keyValue !== "") {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 3:
						this.visitContentOptions = this.visitSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 4:
						this.controlerOptions = this.controlerSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					default:
						return;
				}
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 3:
						this.visitContentOptions = this.visitSourceData;
						break;
					case 4:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},
		// 改变每页显示的条数
		chageSize(size) {
			this.currentPage = 1;
			this.pageSize = size;
			this.getVisitedData();
		},
		// 切换页码
		chagePage(page) {
			this.currentPage = page;
			this.getVisitedData();
		},
		// 获取所有用户
		getAllUsers() {
			let _this = this;
			this.get_allUsers()
				.then(async function (res) {
					if (res && res instanceof Array) {
						let sortArr = [];
						res.forEach((item) => {
							if (item.name) {
								sortArr.push({
									soreCode: _this.getStringCode(getSpell(item.name)),
									...item,
								});
							}
						});
						sortArr.sort((a, b) => {
							return a.soreCode.charCodeAt() - b.soreCode.charCodeAt();
						});
						_this.controlerOptions = sortArr;
						_this.controlerSourceData = sortArr;

						let user = await _this.getUser();
						if (user !== true) {
							_this.form.controler = user.id;
							_this.controleable = true;
						}
						this.getVisitedData();
					}
				})
				.catch((error) => {
					this.getVisitedData();
				});
		},
		// 获取名字首字母，并转成大写字母
		getStringCode(str) {
			const strArr = str && str.split(",");
			let strOne = strArr[0];
			if (strOne.substr(0, 1) === "[") {
				return strOne.substr(1, 1).toUpperCase();
			} else {
				return strOne.substr(0, 1).toUpperCase();
			}
		},
		// 获取二级用户组
		getTwoGroups() {
			this.get_allGroups().then((res) => {
				if (res && res instanceof Array) {
					this.allgroups = res;
					groupFilter.initData(res);
					this.secondSourceData = groupFilter.getNodesByTreeLeval(2);
					this.secondOptions = groupFilter.getNodesByTreeLeval(2);
				}
			});
		},
		// 获取所有的场景名称
		getAllScenes() {
			this.get_AllScenes().then((res) => {
				if (res) {
					res = res.map((ele, idx) => {
						return {
							name: ele.name,
							id: idx + 1,
						};
					});
					this.visitSourceData = res;
					this.visitContentOptions = this.visitSourceData;
				}
			});
		},
		// 选择二级用户组获取三级用户组
		secoundChange(e) {
			this.form.groupId = e;
			this.form.thridorgName = "";
			this.thirdSourceData = groupFilter.getChildNodesById(e);
			this.thirdOptions = groupFilter.getChildNodesById(e);
		},
		// 清空二级用户组的同时清空三级用户组选项
		clearTag() {
			this.thirdOptions = [];
			this.form.thridorgName = "";
		},
		// 选择时间范围
		changeTime(flag) {
			// flag为true时代表起始时间的选择
			// flag为false时代表结束时间的选择
			if (flag) {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			} else {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			}
		},
		// 校验选择的时间是否符合规范
		checkTime(flag, start, end) {
			if (flag) {
				if (new Date(start) > new Date(end)) {
					this.form.startTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "开始时间大于结束时间",
					});
				}
			} else {
				if (new Date(start) > new Date(end)) {
					this.form.endTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "结束时间小于开始时间",
					});
				}
			}
		},
		// 判断时间条件是否完整
		timeIsFull() {
			if (this.form.startTime && this.form.endTime) {
				return true;
			} else if (this.form.startTime && !this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "结束时间不能为空",
				});
				return false;
			} else if (!this.form.startTime && this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "开始时间不能为空",
				});
				return false;
			} else {
				return true;
			}
		},
		// 设置默认日期
		async fetchDate() {
			var now = new Date();
			var year = now.getFullYear();
			var month = now.getMonth() + 1;
			var day = now.getDate();
			var hour = now.getHours();
			var min = now.getMinutes();
			var seconds = now.getSeconds();
			const after =
				year +
				"-" +
				(month < 10 ? "0" + month : month) +
				"-" +
				(day < 10 ? "0" + day : day);
			const before = await this.getBeforeDate(Date.now());

			this.form.startTime = before;
			this.form.endTime = after;
		},

		// 获取当前日期30天前的日期
		getBeforeDate(temp) {
			return new Promise((resove, reject) => {
				var timestr = new Date(temp - 60 * 60 * 24 * 30 * 1000);

				var year = timestr.getFullYear();

				var month = timestr.getMonth() + 1;

				var date = timestr.getDate();

				var hour = timestr.getHours();

				var minute = timestr.getMinutes();

				var second = timestr.getSeconds();

				var datetime =
					year +
					"-" +
					(month < 10 ? "0" + month : month) +
					"-" +
					(date < 10 ? "0" + date : date);

				resove(datetime);
			});
		},
		// 导出按钮
		exportXls() {
			this.exportExcel_();
		},
		// 接口数据导出
		exportExcel_() {
			let params = {
				userId: this.form.controler,
				groupId: this.form.secondorgName,
				logContent: this.form.visitContent,
				startTime: this.form.startTime
					? this.form.startTime + " " + "00:00:01"
					: "",
				endTime: this.form.endTime ? this.form.endTime + " " + "23:59:59" : "",
				// 1升序2降序
				sortTypeStatus: 2,
			};
			this.get_ExportXsl(params).then((res) => {
				if (res) {
					var blob = new Blob([res], { type: "application/xls" });
					const url = window.URL.createObjectURL(blob);
					const fileDownloader = document.createElement("a");
					fileDownloader.href = url;
					fileDownloader.id = "fileDown";
					fileDownloader.download = "访问日志.xls";
					fileDownloader.click();
					document.removeChild(document.getElementById("fileDown"));
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg success",
						message: "导出成功",
					});
				}
			});
		},
		// 条件查询
		searchData() {
			this.currentPage = 1;
			this.tableData = [];
			this.visitToSearch = "";
			this.visitToSearch = this.form.visitContent;
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			this.getVisitedData();
		},
		// 获取访问日志数据
		getVisitedData() {
			// this.tableData = []
			let params = {
				userId: this.form.controler,
				groupId: this.form.secondorgName,
				logContent: this.visitToSearch,
				startTime: this.form.startTime
					? this.form.startTime + " " + "00:00:01"
					: "",
				endTime: this.form.endTime ? this.form.endTime + " " + "23:59:59" : "",
				currentPage: this.currentPage,
				pageRecords: this.pageSize,
				// 1升序2降序
				sortTypeStatus: 2,
			};
			let param = {};
			for (let item in params) {
				if (params[item] != "") {
					param[item] = params[item];
				}
			}
			this.get_VisitedData(param).then((res) => {
				if (res) {
					let result = res.logList.map((ele) => {
						if (ele.groups) {
							let groups = groupFilter.filter(ele.groups);
							if (this.form.groupId && this.form.groupId != "") {
								groups = groups.filter((item) => {
									let maching = item.map((group) => {
										return group.id;
									});
									if (maching.includes(this.form.groupId)) {
										return item;
									}
								});
							}
							if (groups.length > 0) {
								groups[0].forEach((item) => {
									if (item.treeLeval === 2) {
										ele.groupName = item.name;
									} else if (item.treeLeval === 3) {
										ele.thirdGroup = item.name;
									}
								});
							}
						} else {
							ele.groupName = "";
							ele.thirdGroup = "";
						}
						return ele;
					});
					this.tableData = this.tableData.concat(result);
					this.totalCount = res.totalSize;
				}
			});
		},
		tableListener() {
			console.log("监听表格dom对象的滚动事件");
			let timer = null;
			this.totalCount = 200;
			let that = this;
			let dom = that.$refs.myTable.bodyWrapper;
			dom.addEventListener("scroll", function () {
				const scrollDistance =
					dom.scrollHeight - dom.scrollTop - dom.clientHeight;
				if (scrollDistance <= 0 && that.tableData.length > 0) {
					that.currentPage += 1;
					if (that.currentPage <= Math.ceil(that.totalCount / 20)) {
						if (timer) {
							return;
						}
						timer = setTimeout(() => {
							clearTimeout(timer);
							timer = null;
							that.getVisitedData();
						}, 500);
					}
				}
			});
		}
	},
};
</script>

<style lang="less" scoped>
.visitCard {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 0 20px;
	.conHeader {
		background: #fff;
		display: flex;
		flex-direction: row;
		align-items: center;
		.filterList {
			flex: 1;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow: hidden;
		}
		.el-form {
			flex: 1;
			.el-form-item {
				width: 31%;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				align-items: center;
				.form-item-label {
					min-width: 100px;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}
			}
		}
		::v-deep.el-form-item__label {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		/deep/.el-form-item__content {
			width: 100%;
			line-height: 30px;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		.filter-group {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			flex-wrap: nowrap;
		}
		.filterBtn {
			margin-left: auto;
			height: 50px;
			display: flex;
			align-items: center;
		}
	}
	.conBody {
		width: 100%;
		.empty {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			img {
				width: 322px;
				height: 224px;
			}
		}
		/deep/.el-table {
			height: 100%;
			.el-table__body-wrapper {
				.el-table__empty-block {
					height: calc(100vh - 260px) !important;
				}
			}
		}
	}
	.conFoot {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 40px;
		background: #f7f7f7;
		padding: 0 10px;
		.refresh {
			cursor: pointer;
			animation: run 1s infinite;
		}
		.refresh_active {
			animation: run 2s infinite;
		}
		@keyframes run {
			0% {
				transform: rotateZ(0deg);
			}
			100% {
				transform: rotateZ(360deg);
			}
		}
		.pageTips {
			font-size: 14px;
			margin-left: auto;
		}
	}
}
</style>
