<template>
	<div class="conCard">
		<div class="conHeader">
			<div class="filterList">
				<el-form ref="form" :label-position="labelPosition" :model="form">
					<div class="filter-group">
						<el-form-item prop="controler">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ContrlCard.form_labels.label_1') + ':'"
							>
								{{ $t("zh_CN.ContrlCard.form_labels.label_1") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.controler"
								clearable
								reserve-keyword
								:disabled="controleable"
								@visible-change="
									(e) => {
										keyValueChange(e, 3);
									}
								"
								:placeholder="$t('zh_CN.ContrlCard.placeholder.place_1')"
								style="flex: 1"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="controlerSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 3);
											}
										"
										:placeholder="$t('zh_CN.ContrlCard.placeholder.place_4')"
									></el-input>
								</div>
								<el-option
									v-for="ele in controlerOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="secondorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ContrlCard.form_labels.label_2') + ':'"
							>
								{{ $t("zh_CN.ContrlCard.form_labels.label_2") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.secondorgName"
								:placeholder="$t('zh_CN.ContrlCard.placeholder.place_2')"
								clearable
								@change="secoundChange"
								@clear="clearTag"
								@visible-change="
									(e) => {
										keyValueChange(e, 1);
									}
								"
								style="flex: 1"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="secondSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 1);
											}
										"
										:placeholder="$t('zh_CN.ContrlCard.placeholder.place_4')"
									></el-input>
								</div>
								<el-option
									v-for="ele in secondOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="thridorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.ContrlCard.form_labels.label_3') + ':'"
							>
								{{ $t("zh_CN.ContrlCard.form_labels.label_3") + ":" }}
							</div>
							<el-tooltip
								class="item"
								effect="light"
								v-if="form.secondorgName === ''"
								:content="$t('zh_CN.ContrlCard.placeholder.place_5')"
								placement="top-end"
							>
								<el-select
									class="scenatorStyle"
									popper-class="scenator_selectPopper controlLogs_selectPopper"
									:disabled="form.secondorgName !== '' ? false : true"
									v-model="form.thridorgName"
									:placeholder="$t('zh_CN.ContrlCard.placeholder.place_3')"
									clearable
									style="width: 100%"
								>
									<el-option
										v-for="ele in thirdOptions"
										:key="ele.id"
										:label="ele.name"
										:value="ele.id"
									>
									</el-option>
								</el-select>
							</el-tooltip>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-if="form.secondorgName !== ''"
								:disabled="form.secondorgName !== '' ? false : true"
								v-model="form.thridorgName"
								clearable
								@visible-change="
									(e) => {
										keyValueChange(e, 2);
									}
								"
								:placeholder="$t('zh_CN.ContrlCard.placeholder.place_3')"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="thirdSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 2);
											}
										"
										:placeholder="$t('zh_CN.ContrlCard.placeholder.place_4')"
									></el-input>
								</div>
								<el-option
									v-for="ele in thirdOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.name"
								>
								</el-option>
							</el-select>
						</el-form-item>
					</div>
					<div class="filter-group">
						<el-form-item>
							<div
								class="form-item-label"
								:title="$t('zh_CN.ContrlCard.form_labels.label_4') + ':'"
							>
								{{ $t("zh_CN.ContrlCard.form_labels.label_4") + ":" }}
							</div>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								v-model="form.startTime"
								:placeholder="$t('zh_CN.ContrlCard.placeholder.place_6')"
								:picker-options="picker_disabled"
								value-format="yyyy-MM-dd"
								@change="changeTime(true)"
								style="width: 48%"
							>
							</el-date-picker>
							<span class="time-splite">—</span>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								:placeholder="$t('zh_CN.ContrlCard.placeholder.place_7')"
								:picker-options="picker_disabled"
								v-model="form.endTime"
								value-format="yyyy-MM-dd"
								@change="changeTime(false)"
								style="width: 48%"
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item>
							<div class="filterBtn">
								<el-button
									type="secondary--button"
									plain
									@click="searchData"
									class="scenatorStyle"
									>{{ $t("zh_CN.ContrlCard.btn_1") }}</el-button
								>
								<el-button
									type="secondary--button"
									plain
									@click="initData('form')"
									class="scenatorStyle"
									>{{ $t("zh_CN.ContrlCard.btn_2") }}</el-button
								>
								<el-button
									type="main--button"
									plain
									class="scenatorStyle"
									:disabled="tableData.length > 0 ? false : true"
									@click="exportXls"
									>{{ $t("zh_CN.ContrlCard.btn_3") }}</el-button
								>
							</div>
						</el-form-item>
					</div>
				</el-form>
			</div>
		</div>
		<div class="conBody scenatorStyle">
			<el-table
				v-loading="loading"
				element-loading-background="rgba(255, 255, 255, 0.5)"
				ref="myTable"
				:data="tableData"
				border
				stripe
				:default-sort="{ prop: 'eventTime', order: 'descending' }"
				:max-height="tableHeight"
				id="out-table"
			>
				<template v-for="(item, idx) in columns">
					<el-table-column
						:key="idx"
						:prop="item.prop"
						:label="item.label"
						align="center"
						:sortable="item.sortable"
						:sort-method="item.sort_method"
					>
					</el-table-column>
				</template>
				<template slot="empty">
					<div class="empty">
						<img src="@/assets/empty.png" alt="暂无数据！" />
						<span>{{ $t("zh_CN.ContrlCard.empty") }}</span>
					</div>
				</template>
				<div class="no_more">无更多数据</div>
			</el-table>
		</div>
		<!-- <div class="conFoot">
            <el-pagination
                :current-page.sync="currentPage"
                :page-sizes="[30, 40, 50, 60]"
                :page-size="pageSize"
                layout="sizes, prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <i class="el-icon-refresh-right refresh"></i>
            <span class="pageTips">当前显示从1到{{pageSize}}页共{{totalCount}}条数据</span>
        </div> -->
	</div>
</template>

<script>
import { mapActions } from "vuex";
import FileSaver from "file-saver";
import XLSX from "xlsx";
import { getSpell } from "../../utils/jian-pinyin";
import GroupFilter from "../../utils/groupFilter.js";
const groupFilter = new GroupFilter();
export default {
	name: "ContrlCard",
	data() {
		return {
			controlerSourceData: [],
			controlerOptions: [],

			secondSourceData: [],
			secondOptions: [],

			thirdSourceData: [],
			thirdOptions: [],

			thirdShow: false,
			picker_disabled: {
				disabledDate: (time) => {
					return time.getTime() > Date.now();
				},
			},
			columns: [
				{
					prop: "name",
					label: this.$t("zh_CN.ContrlCard.form_labels.label_1"),
					sortable: true,
					sort_method: (a, b) => {
						if (!a.name) a.name = " ";
						if (!b.name) b.name = " ";
						return a.name.localeCompare(b.name);
					},
				},
				{
					prop: "groupName",
					label: this.$t("zh_CN.ContrlCard.form_labels.label_2"),
					sortable: true,
					sort_method: (a, b) => {
						if (!a.groupName || a.groupName === "") a.groupName = " ";
						if (!b.groupName || b.groupName === "") b.groupName = " ";
						return a.groupName.localeCompare(b.groupName);
					},
				},
				{
					prop: "thridorgName",
					label: this.$t("zh_CN.ContrlCard.form_labels.label_3"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.thridorgName === "") a.thridorgName = " ";
						if (b.thridorgName === "") b.thridorgName = " ";
						return a.thridorgName.localeCompare(b.thridorgName);
					},
				},
				{
					prop: "eventTime",
					label: this.$t("zh_CN.ContrlCard.form_labels.label_4"),
					sortable: true,
				},
				{
					prop: "ip",
					label: this.$t("zh_CN.ContrlCard.ip_address"),
				},
			],
			tableData: [],
			form: {
				controler: "",
				groupId: "",
				secondorgName: "",
				thridorgName: "",
				startTime: "",
				endTime: "",
			},

			allgroups: [],
			secondGroupOrder: [],
			thirdGroupOrder: [],

			totalCount: 0,
			pageSize: 20,
			pageNo: 1,
			currentNo: 1,

			keyValue: "",

			labelPosition: "left",

			loading: false,

			controleable: false,

			tableHeight: window.innerHeight - 208,
		};
	},
	mounted() {
		this.fetchDate();
		this.getTwoGroups();
		this.getAllUsers();
		this.tableListener();
		this.$nextTick(() => {
			window.onresize = () => {
				this.tableHeight = window.innerHeight - 208;
			};
		});
	},
	methods: {
		...mapActions([
			// 获取登录日志列表数据
			"get_LoginLogsList",
			// 获取所有的用户组
			"get_allGroups",
			// 获取所有的用户
			"get_allUsers",
			// 获取当前用户
			"getCurrentUser",
		]),

		// 获取当前用户
		getUser() {
			return new Promise((resolve) => {
				this.getCurrentUser()
					.then((res) => {
						let isSysGroup = [];
						if (res.groups.length > 0) {
							isSysGroup = res.groups.filter((group) => {
								if (group.sysGroup === true) {
									return group;
								}
							});
						}
						if (isSysGroup.length > 0) {
							resolve(true);
						} else {
							resolve(res);
						}
					})
					.catch((error) => {
						resolve(true);
					});
			});
		},

		// 筛选条件清空
		initData(e) {
			this.form.groupId = "";
			this.$refs[e].resetFields();
			this.pageNo = 1;
			this.totalCount = 0;
			this.pageSize = 20;
			this.tableData = [];
			this.fetchDate();
			this.getAllUsers();
		},
		// 下拉框开启关闭时的数据状态
		keyValueChange(flag, tag) {
			if (!flag) {
				this.keyValue = "";
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 3:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},

		dropDownSearch(e, tag) {
			if (this.keyValue !== "") {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 3:
						this.controlerOptions = this.controlerSourceData.filter((ele) => {
							if (ele.name && ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					default:
						return;
				}
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					case 3:
						this.controlerOptions = this.controlerSourceData;
						break;
					default:
						return;
				}
			}
		},

		// 获取所有用户
		getAllUsers() {
			let _this = this;
			this.get_allUsers()
				.then(async function (res) {
					if (res && res instanceof Array) {
						let sortArr = [];
						res.forEach((item) => {
							if (item.name) {
								sortArr.push({
									soreCode: _this.getStringCode(getSpell(item.name)),
									...item,
								});
							}
						});
						sortArr.sort((a, b) => {
							return a.soreCode.charCodeAt() - b.soreCode.charCodeAt();
						});
						_this.controlerSourceData = sortArr;
						_this.controlerOptions = _this.controlerSourceData;
						let user = await _this.getUser();
						if (user !== true) {
							_this.form.controler = user.id;
							_this.controleable = true;
						}
						this.getLoginLogsData();
					}
				})
				.catch((error) => {
					this.getLoginLogsData();
				});
		},
		// 获取名字首字母，并转成大写字母
		getStringCode(str) {
			const strArr = str && str.split(",");
			let strOne = strArr[0];
			if (strOne.substr(0, 1) === "[") {
				return strOne.substr(1, 1).toUpperCase();
			} else {
				return strOne.substr(0, 1).toUpperCase();
			}
		},
		// 获取二级用户组
		getTwoGroups() {
			this.get_allGroups().then((res) => {
				if (res && res instanceof Array) {
					this.allgroups = res;
					groupFilter.initData(res);
					this.secondSourceData = groupFilter.getNodesByTreeLeval(2);
					this.secondOptions = groupFilter.getNodesByTreeLeval(2);
				}
			});
		},
		// 条件查询
		searchData() {
			this.pageNo = 1;
			this.totalCount = 0;
			this.tableData = [];
			this.getLoginLogsData();
		},
		// 获取登录日志列表数据
		getLoginLogsData() {
			this.loading = true;
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			let param = {
				type: "LOGIN",
				userId: this.form.controler,
				groupId: this.form.groupId,
				startTime: this.form.startTime
					? this.form.startTime + " " + "00:00:00"
					: "",
				endTime: this.form.endTime ? this.form.endTime + " " + "23:59:59" : "",
				pageNo: this.pageNo,
				pageSize: this.pageSize,
			};
			this.get_LoginLogsList(param)
				.then((res) => {
					this.loading = false;
					if (res.eventVOList) {
						let result = res.eventVOList.map((ele) => {
							if (ele.groups) {
								let groups = groupFilter.filter(ele.groups);
								if (this.form.groupId != "") {
									groups = groups.filter((item) => {
										let maching = item.map((group) => {
											return group.id;
										});
										if (maching.includes(this.form.groupId)) {
											return item;
										}
									});
								}
								if (groups.length > 0) {
                  groups[0].forEach((item) => {
                    if (item.treeLeval === 2) {
                      ele.groupName = item.name;
                    } else if (item.treeLeval === 3) {
                      ele.thridorgName = item.name;
                    }
                  });
								}
							} else {
								ele.groupName = "";
								ele.thridorgName = "";
							}
							return ele;
						});
						this.tableData = this.tableData.concat(result);
						this.totalCount = res.total;
						this.currentNo = res.current;
					}
				})
				.catch((err) => {
					this.loading = false;
				});
		},
		// 选择二级用户组获取三级用户组
		secoundChange(e) {
			this.form.groupId = e;
			this.thirdSourceData = groupFilter.getChildNodesById(e);
			this.thirdOptions = groupFilter.getChildNodesById(e);
		},
		// 选择时间范围
		changeTime(flag) {
			// flag为true时代表起始时间的选择
			// flag为false时代表结束时间的选择
			if (flag) {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			} else {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			}
		},
		// 校验选择的时间是否符合规范
		checkTime(flag, start, end) {
			if (flag) {
				if (new Date(start) > new Date(end)) {
					this.form.startTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "开始时间大于结束时间",
					});
				}
			} else {
				if (new Date(start) > new Date(end)) {
					this.form.endTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "结束时间小于开始时间",
					});
				}
			}
		},
		// 判断时间条件是否完整
		timeIsFull() {
			if (this.form.startTime && this.form.endTime) {
				return true;
			} else if (this.form.startTime && !this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "结束时间不能为空",
				});
				return false;
			} else if (!this.form.startTime && this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "开始时间不能为空",
				});
				return false;
			} else {
				return true;
			}
		},
		// 清空二级用户组的同时清空三级用户组选项
		clearTag() {
			this.thirdOptions = [];
			this.form.thridorgName = "";
		},
		// 导出按钮
		exportXls() {
			this.getExportData();
		},
		// 获取需要导出的数据
		getExportData() {
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			let param = {
				type: "LOGIN",
				userId: this.form.controler,
				groupId: this.form.groupId,
				startTime: this.form.startTime
					? this.form.startTime + " " + "00:00:00"
					: "",
				endTime: this.form.endTime ? this.form.endTime + " " + "23:59:59" : "",
				pageNo: 1,
				pageSize: 100000,
			};
			this.get_LoginLogsList(param)
				.then((res) => {
					if (res.eventVOList) {
						this.exportExcel(res.eventVOList);
					}
				})
				.catch((error) => {
					console.log(error);
				});
		},
		//定义导出Excel表格事件
		exportExcel(data) {
			const _headers = [
				["操作人", "二级机构名称", "三级机构名称", "时间段", "IP地址"],
			];
			let result = data.map((ele) => {
				if (ele.groups) {
					let groups = groupFilter.filter(ele.groups);
					if (this.form.groupId != "") {
						groups = groups.filter((item) => {
							let maching = item.map((group) => {
								return group.id;
							});
							if (maching.includes(this.form.groupId)) {
								return item;
							}
						});
					}
					if (groups.length > 0) {
						groups[0].forEach((item) => {
							if (item.treeLeval === 2) {
								ele.groupName = item.name;
							} else if (item.treeLeval === 3) {
								ele.thridorgName = item.name;
							}
						});
					}
				} else {
					ele.groupName = "";
					ele.thridorgName = "";
				}
				return ele;
			});
			let body = [];
			body = result.map((item) => {
				return [].concat([
					item.name,
					item.groupName,
					item.thridorgName,
					item.eventTime,
					item.ip,
				]);
			});
			const tabledata = _headers.concat(body);
			console.log(tabledata);
			let worksheet = XLSX.utils.aoa_to_sheet(tabledata);
			let workbook = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(workbook, worksheet, "第一页");
			XLSX.writeFile(workbook, "登录日志.xlsx");
		},
		// 设置默认日期
		async fetchDate() {
			var now = new Date();
			var year = now.getFullYear();
			var month = now.getMonth() + 1;
			var day = now.getDate();
			var hour = now.getHours();
			var min = now.getMinutes();
			var seconds = now.getSeconds();
			const after =
				year +
				"-" +
				(month < 10 ? "0" + month : month) +
				"-" +
				(day < 10 ? "0" + day : day);
			const before = await this.getBeforeDate(Date.now());

			this.form.startTime = before;
			this.form.endTime = after;
		},

		// 获取当前日期30天前的日期
		getBeforeDate(temp) {
			return new Promise((resove, reject) => {
				var timestr = new Date(temp - 60 * 60 * 24 * 30 * 1000);

				var year = timestr.getFullYear();

				var month = timestr.getMonth() + 1;

				var date = timestr.getDate();

				var hour = timestr.getHours();

				var minute = timestr.getMinutes();

				var second = timestr.getSeconds();

				var datetime =
					year +
					"-" +
					(month < 10 ? "0" + month : month) +
					"-" +
					(date < 10 ? "0" + date : date);

				resove(datetime);
			});
		},

		tableListener() {
			console.log("监听表格dom对象的滚动事件");
			let timer = null;
			let that = this;
			this.totalCount = 200;
			let dom = that.$refs.myTable.bodyWrapper;
			dom.addEventListener("scroll", function () {
				const scrollDistance =
					dom.scrollHeight - dom.scrollTop - dom.clientHeight;
				if (scrollDistance <= 0) {
					if (that.currentNo !== that.pageNo) {
						return;
					}
					that.pageNo += 1;
					if (that.pageNo <= Math.ceil(that.totalCount / that.pageSize)) {
						if (timer) {
							return;
						}
						timer = setTimeout(() => {
							clearTimeout(timer);
							timer = null;
							that.getLoginLogsData();
						}, 500);
					}
				}
			});
		},
	},
};
</script>

<style lang="less" scoped>
.conCard {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 0 20px;
	.conHeader {
		background: #fff;
		display: flex;
		flex-direction: row;
		align-items: center;
		// padding: 0 20px 0 20px;
		.filterList {
			flex: 1;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			overflow: hidden;
		}
		.el-form {
			flex: 1;
			.el-form-item {
				width: 31%;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				align-items: center;
				.form-item-label {
					min-width: 100px;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}
			}
		}
		::v-deep.el-form-item__label {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		/deep/.el-form-item__content {
			width: 100%;
			line-height: 30px;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		.filter-group {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			flex-wrap: nowrap;
		}
		.filterBtn {
			margin-left: auto;
			height: 50px;
			display: flex;
			align-items: center;
		}
	}
	.conBody {
		position: relative;
		width: 100%;
		// padding: 10px 20px 0 20px;
		.empty {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			img {
				width: 322px;
				height: 224px;
			}
		}
		/deep/.el-table {
			height: 100%;
			.el-table__body-wrapper {
				.el-table__empty-block {
					height: calc(100vh - 260px) !important;
				}
			}
		}
	}
	.conFoot {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: 40px;
		background: #f7f7f7;
		padding: 0 10px;
		.refresh {
			cursor: pointer;
			animation: run 1s infinite;
		}
		.refresh_active {
			animation: run 2s infinite;
		}
		@keyframes run {
			0% {
				transform: rotateZ(0deg);
			}
			100% {
				transform: rotateZ(360deg);
			}
		}
		.pageTips {
			font-size: 14px;
			margin-left: auto;
		}
	}
}
</style>
