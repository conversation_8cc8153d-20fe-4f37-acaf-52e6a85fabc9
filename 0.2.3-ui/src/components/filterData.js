/**
 *  useable: 用于处理AIMS操作日志数据的条件筛选功能
 * 
 *  实例化时需要传入从后台获取的所有数据
 *  参数可选，初始化传参将返回根据传入的参数过滤返回的数据,适用于给默认值的情况
 * 
 *  可传参数如下:
 *  @param {Arrary} tableData 所有的数据源
 *  @param {String} controlName 操作名称
 *  @param {String} controler 操作人
 *  @param {String} secondorgName 二级机构名称
 *  @param {String} thridorgName 三级机构名称
 *  @param {String} startTime 开始时间
 *  @param {String} endTime 结束时间
 *  @param {String} deviceName 装置名称
 *  @param {String} changeContent 变更内容
 * 
 *  内置调用方法如下:
 *  @param {Function} get_FilterListOptions 初始化下拉框数据
*/
class FilterData {
    constructor({
        tableData = [],
        controlName = "",
        controler = "",
        secondorgName = "",
        thridorgName = "",
        startTime = "",
        endTime = "",
        deviceName = "",
        changeContent = ""
    }) {

        // 收集所有的参数
        this.form = {
            behavior: controlName,
            createdBy: controler,
            levelTwoOrganization: secondorgName,
            levelThreeOrganization: thridorgName,
            device: deviceName,
            operationDescription: changeContent
        }

        // 开始时间，截止时间参数
        this.startTime = startTime
        this.endTime = endTime

        // 接受数据源
        this.allData = tableData

        // 操作名称下拉框列表
        this.controlList = []

        // 操作人下拉列表
        this.controlerList = []

        // 二级机构名称下拉列表
        this.secondOrgNameList = []

        // 三级机构名称下拉列表
        this.thirdOrgNameList = []

        // 装置名称下拉列表
        this.deviceNameList = []

        // 装置内容下拉列表
        this.changeContentList = []
    }


    // 返回所有的下拉框列表数据
    get_FilterListOptions() {
        return new Promise(resolve=> {
            let _this = this
            _this.allData.forEach(ele=> {
                // 过滤操作名称
                if(_this.controlList.indexOf(ele.behavior) === -1) {
                    _this.controlList.push(ele.behavior)
                }

                // 过滤操作人
                if(_this.controlerList.indexOf(ele.createdBy) === -1) {
                    _this.controlerList.push(ele.createdBy)
                }

                // 过滤二级机构名称
                if(_this.secondOrgNameList.indexOf(ele.levelTwoOrganization) === -1) {
                    _this.secondOrgNameList.push(ele.levelTwoOrganization)
                }

                // 过滤装置名称
                if(_this.deviceNameList.indexOf(ele.device) === -1) {
                    _this.deviceNameList.push(ele.device)
                }

                // 变更内容
                if(_this.changeContentList.indexOf(ele.operationDescription) === -1) {
                    _this.changeContentList.push(ele.operationDescription)
                }
            })

            // 生成操作名称下拉数据
            let controlList = _this.controlList.map((item,idx)=> {
                return item = {
                    name: item,
                    id: 'a' + idx
                }
            })

            // 生成操作人下拉数据
            let controlerList = _this.controlerList.map((item,idx)=> {
                return item = {
                    name: item,
                    id: 'b' + idx
                }
            })

            // 生成二级机构名称下拉数据
            let secondOrgNameList =_this.secondOrgNameList.map((item,idx)=> {
                return item = {
                    name: item,
                    id: 'c' + idx
                }
            })

            // 生成装置名称下拉数据
            let deviceNameList =_this.deviceNameList.map((item,idx)=> {
                return item = {
                    name: item,
                    id: 'd' + idx
                }
            })

            // 生成变更内容下拉数据
            let changeContentList = _this.changeContentList.map((item,idx)=> {
                return item = {
                    name: item,
                    id: 'e' + idx
                }
            })
            resolve({
                controlList,
                controlerList,
                secondOrgNameList,
                deviceNameList,
                changeContentList
            })
        })
    }

    // 根据二级机构名称过滤三级机构名称
    filter_thirdOrgName(secondName) {
        this.thirdOrgNameList = []
        if(secondName === '') return;
        return new Promise(resove=> {
            this.allData.forEach(ele=> {
                if((ele.levelTwoOrganization === secondName) && (this.thirdOrgNameList.indexOf(ele.levelThreeOrganization) === -1)) {
                    if(ele.levelThreeOrganization) {
                        this.thirdOrgNameList.push(ele.levelThreeOrganization)
                    }
                }
            })
    
            let thirdList = this.thirdOrgNameList.map((item,idx)=> {
                return {
                    name: item,
                    id: 'f' + idx
                }
            })
            resove({ thirdList })
        })

    }

    // 根据条件筛选数据
    get_filterTabledata({
        controlName = "",
        controler = "",
        secondorgName = "",
        thridorgName = "",
        startTime = "",
        endTime = "",
        deviceName = "",
        changeContent = ""
    }) {
        let reaciveParam = {
            behavior: controlName,
            createdBy: controler,
            levelTwoOrganization: secondorgName,
            levelThreeOrganization: thridorgName,
            device: deviceName,
            operationDescription: changeContent
        }
        this.form = Object.assign(this.form, reaciveParam)
        this.startTime = startTime
        this.endTime = endTime

        // 筛选有效参数
        let param = Object.keys(this.form).filter(ele=> {
            // 默认只处理参数为字符串的格式，有其他格式请自行添加
            if(this.form[ele] !== "") {
                return ele
            }
        })

        // 生成过滤条件
        let filters = ``
        param.forEach((ele, idx)=> {
            if(idx < param.length - 1) {
                filters += `('${this.form[ele]}' === data['${ele}']) && `
            }else {
                filters += `('${this.form[ele]}' === data['${ele}'])`
            }
        })

        return new Promise(resolve=> {
            let sourceData = this.allData

            // 如果时间条件存在，先过滤时间范围内的数据
            if((this.startTime !== '') && (this.endTime != '')) {
                let statrtTimeStamp = this.exchangeTimeStamp(this.startTime)
                let endTimeStamp = this.exchangeTimeStamp(this.endTime)
                sourceData = this.allData.filter(td=> {
                    if(statrtTimeStamp <= this.exchangeTimeStamp(td.gmtCreated) &&
                        (endTimeStamp >= this.exchangeTimeStamp(td.gmtCreated))) {
                        return td
                    }
                })
            }
            // 通过生成的有效筛选条件，过滤出符合条件的数据
            if(param.length > 0) {
                let result = sourceData.filter(data=> {
                    if(
                        eval(filters)
                    ) {
                        return data
                    }
                })
                resolve(result)
            }else {
                resolve(sourceData)
            }
        })
    }

    // 时间字符串转时间戳
    exchangeTimeStamp(timeStr) {
        if(timeStr === '') {
            return;
        }else {
            timeStr = timeStr.substring(0,19);    
            timeStr = timeStr.replace(/-/g,'/'); //必须把日期'-'转为'/'
            let timestamp = new Date(timeStr).getTime()
            return timestamp
        }
    }

}

export default FilterData