<template>
	<div class="visitCard">
		<div class="conHeader">
			<div class="filterList">
				<el-form ref="form" :label-position="labelPosition" :model="form">
					<div class="filter-group">
						<el-form-item prop="secondorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitPerCard.form_labels.label_1') + ':'"
							>
								{{ $t("zh_CN.VisitPerCard.form_labels.label_1") + ":" }}
							</div>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-model="form.secondorgName"
								:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_1')"
								clearable
								@change="secoundChange"
								@clear="clearTag"
								@visible-change="
									(e) => {
										keyValueChange(e, 1);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="secondSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 1);
											}
										"
										:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_5')"
									></el-input>
								</div>
								<el-option
									v-for="ele in secondOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item prop="thridorgName">
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitPerCard.form_labels.label_2') + ':'"
							>
								{{ $t("zh_CN.VisitPerCard.form_labels.label_2") + ":" }}
							</div>
							<el-tooltip
								class="item"
								effect="light"
								v-if="form.secondorgName === ''"
								:content="$t('zh_CN.VisitPerCard.placeholder.place_2')"
								placement="top-end"
							>
								<el-select
									class="scenatorStyle"
									popper-class="scenator_selectPopper controlLogs_selectPopper"
									:disabled="form.secondorgName !== '' ? false : true"
									v-model="form.thridorgName"
									:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_1')"
									clearable
									style="width: 100%"
								>
									<el-option
										v-for="ele in thirdOptions"
										:key="ele.id"
										:label="ele.name"
										:value="ele.id"
									>
									</el-option>
								</el-select>
							</el-tooltip>
							<el-select
								class="scenatorStyle"
								popper-class="scenator_selectPopper controlLogs_selectPopper"
								v-if="form.secondorgName !== ''"
								:disabled="form.secondorgName !== '' ? false : true"
								v-model="form.thridorgName"
								clearable
								@change="thirdChange"
								:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_1')"
								@visible-change="
									(e) => {
										keyValueChange(e, 2);
									}
								"
								style="width: 100%"
							>
								<div
									class="filter-input"
									style="
										position: sticky;
										top: 0%;
										padding: 4px 0;
										background: #fff;
										z-index: 999;
									"
								>
									<el-input
										size="small"
										v-if="thirdSourceData.length >= 7"
										style="width: 90%; margin: 0 0 2% 5%"
										suffix-icon="el-icon-search"
										v-model="keyValue"
										@input="
											(e) => {
												dropDownSearch(e, 2);
											}
										"
										:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_5')"
									></el-input>
								</div>
								<el-option
									v-for="ele in thirdOptions"
									:key="ele.id"
									:label="ele.name"
									:value="ele.id"
								>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item>
							<div
								class="form-item-label"
								:title="$t('zh_CN.VisitPerCard.form_labels.label_3') + ':'"
							>
								{{ $t("zh_CN.VisitPerCard.form_labels.label_3") + ":" }}
							</div>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48%"
								v-model="form.startTime"
								:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_3')"
								:picker-options="picker_disabled"
								value-format="yyyy-MM-dd"
								@change="changeTime(true)"
							>
							</el-date-picker>
							<span class="time-splite">—</span>
							<el-date-picker
								class="scenatorStyle"
								popper-class="scenatorStyle"
								type="date"
								clearable
								prefix-icon="el-icon-date"
								style="width: 48%"
								:placeholder="$t('zh_CN.VisitPerCard.placeholder.place_4')"
								:picker-options="picker_disabled"
								v-model="form.endTime"
								value-format="yyyy-MM-dd"
								@change="changeTime(false)"
							>
							</el-date-picker>
						</el-form-item>
					</div>
					<div class="filter-group">
						<div class="filterBtn">
							<el-button
								plain
								@click="searchData"
								type="secondary--button"
								class="scenatorStyle"
								>{{ $t("zh_CN.VisitPerCard.btn_1") }}</el-button
							>
							<el-button
								plain
								@click="initData('form')"
								type="secondary--button"
								class="scenatorStyle"
								>{{ $t("zh_CN.VisitPerCard.btn_2") }}</el-button
							>
						</div>
					</div>
				</el-form>
			</div>
		</div>
		<div class="conBody scenatorStyle">
			<el-table
				:data="tableData"
				border
				stripe
				:default-sort="{ prop: 'visitPercentage', order: 'descending' }"
				max-height="803"
				id="out-table"
			>
				<el-table-column
					v-for="(item, idx) in columns"
					:key="idx"
					:prop="item.prop"
					:label="item.label"
					:sortable="item.sortable"
					:sort-method="item.sort_method"
					align="center"
				>
				</el-table-column>
				<template slot="empty">
					<div class="empty">
						<img src="@/assets/empty.png" alt="暂无数据！" />
						<span>{{ $t("zh_CN.VisitPerCard.empty") }}</span>
					</div>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { mapActions } from "vuex";
import GroupFilter from "../../utils/groupFilter.js";
const groupFilter = new GroupFilter();
export default {
	name: "VisitCard",
	data() {
		return {
			labelPosition: "left",
			secondSourceData: [],
			secondOptions: [],
			thirdSourceData: [],
			thirdOptions: [],
			picker_disabled: {
				disabledDate: (time) => {
					return time.getTime() > Date.now();
				},
			},
			form: {
				secondorgName: "",
				thridorgName: "",
				startTime: "",
				endTime: "",
				groupId: "",
			},
			tableData: [],
			columns: [
				{
					prop: "logContent",
					label: this.$t("zh_CN.VisitPerCard.feature_name"),
					sortable: true,
					sort_method: (a, b) => {
						if (a.logContent === "") a.logContent = " ";
						if (b.logContent === "") b.logContent = " ";
						return a.logContent.localeCompare(b.logContent);
					},
				},
				{
					prop: "accountNum",
					label: this.$t("zh_CN.VisitPerCard.account_number"),
					sortable: true,
				},
				{
					prop: "visitAccountNum",
					label: this.$t("zh_CN.VisitPerCard.visitor_number"),
					sortable: true,
				},
				{
					prop: "visitTimes",
					label: this.$t("zh_CN.VisitPerCard.visit_count_number"),
					sortable: true,
				},
				{
					prop: "visitPercentage",
					label: this.$t("zh_CN.VisitPerCard.visit_rate"),
					sortable: true,
					sort_method: (a, b) => {
						let before = "",
							end = "";
						if (a.visitPercentage === "") {
							before = " ";
						} else {
							before = a.visitPercentage.split("%")[0];
						}
						if (b.visitPercentage === "") {
							end = " ";
						} else {
							end = b.visitPercentage.split("%")[0];
						}
						return before - end;
					},
				},
			],

			keyValue: "",
		};
	},
	mounted() {
		this.fetchDate();
		this.getTwoGroups();
	},
	methods: {
		...mapActions([
			"get_VisitedPerData",
			"get_allGroups", // 获取所有的用户组
		]),

		// 筛选条件清空
		initData(e) {
			this.form.groupId = "";
			this.$refs[e].resetFields();
			this.fetchDate();
		},

		// 下拉框开启关闭时的数据状态
		keyValueChange(flag, tag) {
			if (!flag) {
				this.keyValue = "";
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					default:
						return;
				}
			}
		},

		dropDownSearch(e, tag) {
			if (this.keyValue !== "") {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData.filter((ele) => {
							if (ele.name.includes(this.keyValue)) {
								return ele;
							}
						});
						break;
					default:
						return;
				}
			} else {
				switch (tag) {
					case 1:
						this.secondOptions = this.secondSourceData;
						break;
					case 2:
						this.thirdOptions = this.thirdSourceData;
						break;
					default:
						return;
				}
			}
		},
		// 获取二级用户组
		getTwoGroups() {
			this.get_allGroups().then((res) => {
				if (res && res instanceof Array) {
					this.allgroups = res;
					groupFilter.initData(res);
					this.secondSourceData = groupFilter.getNodesByTreeLeval(2);
					this.secondOptions = groupFilter.getNodesByTreeLeval(2);
				}
			});
		},
		// 选择二级用户组获取三级用户组
		secoundChange(e) {
			this.form.groupId = e;
			this.form.thridorgName = "";
			this.thirdSourceData = groupFilter.getChildNodesById(e);
			this.thirdOptions = groupFilter.getChildNodesById(e);
		},
		// 选择三级用户组
		thirdChange(e) {
			if (e !== "") {
				this.form.groupId = e;
			} else {
				this.form.groupId = this.form.secondorgName;
			}
		},
		// 清空二级用户组的同时清空三级用户组选项
		clearTag() {
			this.thirdOptions = [];
			this.form.thridorgName = "";
		},
		// 选择时间范围
		changeTime(flag) {
			// flag为true时代表起始时间的选择
			// flag为false时代表结束时间的选择
			if (flag) {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			} else {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			}
		},
		// 校验选择的时间是否符合规范
		checkTime(flag, start, end) {
			if (flag) {
				if (new Date(start) > new Date(end)) {
					this.form.startTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "开始时间大于结束时间",
					});
				}
			} else {
				if (new Date(start) > new Date(end)) {
					this.form.endTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "结束时间小于开始时间",
					});
				}
			}
		},
		// 判断时间条件是否完整
		timeIsFull() {
			if (this.form.startTime && this.form.endTime) {
				return true;
			} else if (this.form.startTime && !this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "结束时间不能为空",
				});
				return false;
			} else if (!this.form.startTime && this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "开始时间不能为空",
				});
				return false;
			} else {
				return true;
			}
		},
		// 设置默认日期
		async fetchDate() {
			var now = new Date();
			var year = now.getFullYear();
			var month = now.getMonth() + 1;
			var day = now.getDate();
			var hour = now.getHours();
			var min = now.getMinutes();
			var seconds = now.getSeconds();
			const after =
				year +
				"-" +
				(month < 10 ? "0" + month : month) +
				"-" +
				(day < 10 ? "0" + day : day);
			const before = await this.getBeforeDate(Date.now());

			this.form.startTime = before;
			this.form.endTime = after;

			this.getVisitedPreData();
		},

		// 获取当前日期30天前的日期
		getBeforeDate(temp) {
			return new Promise((resove, reject) => {
				var timestr = new Date(temp - 60 * 60 * 24 * 30 * 1000);

				var year = timestr.getFullYear();

				var month = timestr.getMonth() + 1;

				var date = timestr.getDate();

				var hour = timestr.getHours();

				var minute = timestr.getMinutes();

				var second = timestr.getSeconds();

				var datetime =
					year +
					"-" +
					(month < 10 ? "0" + month : month) +
					"-" +
					(date < 10 ? "0" + date : date);

				resove(datetime);
			});
		},
		searchData() {
			this.getVisitedPreData();
		},
		// 获取访问率数据
		getVisitedPreData() {
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			this.tableData = [];
			let params = {
				groupId: this.form.groupId,
				startTime: this.form.startTime,
				endTime: this.form.endTime,
			};
			let param = {};
			for (let item in params) {
				if (params[item] != "") {
					param[item] = params[item];
				}
			}
			this.get_VisitedPerData(param).then((res) => {
				if (res && res instanceof Array) {
					this.tableData = res;
				}
			});
		},
	},
};
</script>

<style lang="less" scoped>
.visitCard {
	display: flex;
	flex-direction: column;
	height: 100%;
	padding: 0 20px;
	.conHeader {
		background: #fff;
		display: flex;
		flex-direction: row;
		align-items: center;
		.filterList {
			flex: 1;
			display: flex;
			flex-direction: row;
			padding-top: 20px;
		}
		.el-form {
			flex: 1;
			.el-form-item {
				width: 31%;
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				align-items: center;
				.form-item-label {
					min-width: 100px;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}
			}
		}
		::v-deep .el-form-item__label {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		::v-deep .el-button {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		/deep/.el-form-item__content {
			width: 100%;
			line-height: 30px;
			display: flex;
			flex-direction: row;
			flex-wrap: nowrap;
		}
		.filter-group {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			flex-wrap: nowrap;
		}
		.filterBtn {
			margin-left: auto;
			height: 50px;
		}
	}
	.conBody {
		width: 100%;
		.empty {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			img {
				width: 322px;
				height: 224px;
			}
		}
		/deep/.el-table {
			height: 100%;
			.el-table__body-wrapper {
				height: 747px;
			}
		}
	}
}
</style>
