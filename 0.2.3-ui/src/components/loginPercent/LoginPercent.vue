<template>
	<div class="loginPercent">
		<div class="bread">
			<ul class="breadList">
				<li
					class="breadItem"
					v-for="(item, idx) in breadList"
					:key="idx"
					@click="changeTitle(item, idx)"
				>
					<span :class="idx === breadList.length - 1 ? 'active' : ''">{{
						item.name
					}}</span>
					<i class="el-icon-arrow-right" v-if="idx < breadList.length - 1"></i>
				</li>
			</ul>
		</div>
		<div class="lpHeader">
			<div class="filterItem">
				<span style="margin-right: 20px"
					>{{ $t("zh_CN.LoginPercent.form_labels.label_1") }}:</span
				>
				<el-date-picker
					class="scenatorStyle"
					popper-class="scenatorStyle"
					type="date"
					clearable
					prefix-icon="el-icon-date"
					style="width: 200px"
					v-model="form.startTime"
					:placeholder="$t('zh_CN.LoginPercent.placeholder.place_1')"
					:picker-options="picker_disabled"
					value-format="yyyy-MM-dd"
					@change="changeTime(true)"
				>
				</el-date-picker>
				<span class="time-splite">—</span>
				<el-date-picker
					class="scenatorStyle"
					popper-class="scenatorStyle"
					type="date"
					clearable
					prefix-icon="el-icon-date"
					style="width: 200px"
					:placeholder="$t('zh_CN.LoginPercent.placeholder.place_2')"
					:picker-options="picker_disabled"
					v-model="form.endTime"
					value-format="yyyy-MM-dd"
					@change="changeTime(false)"
				>
				</el-date-picker>
			</div>
			<el-button
				plain
				type="secondary--button"
				class="scenatorStyle"
				@click="searchData"
				>{{ $t("zh_CN.LoginPercent.btn_1") }}</el-button
			>
			<!-- <el-button plain>重置 <i class="el-icon-refresh"></i></el-button> -->
		</div>
		<div class="filter-tips">
			<i class="el-icon-warning-outline"></i>
			<span class="tips-content">{{ $t("zh_CN.LoginPercent.tips") }}</span>
		</div>
		<div class="echarts">
			<div class="chart_wrapper">
				<div class="pr_item" v-show="chartSourceData.data.length">
					<i class="el-icon-arrow-left" @click="pageChage('pre')"></i>
				</div>
				<div id="cylinderChart"></div>
				<div class="next_item" v-show="chartSourceData.data.length">
					<i class="el-icon-arrow-right" @click="pageChage('next')"></i>
				</div>
			</div>
			<div class="chart_title">
				{{ labelTitle }}{{ $t("zh_CN.LoginPercent.title") }}
			</div>
		</div>
	</div>
</template>

<script>
import { mapActions } from "vuex";
import GroupFilter from "../../utils/groupFilter.js";
const groupFilter = new GroupFilter();
export default {
	name: "LoginPercent",
	data() {
		return {
			labelTitle: "",
			currentGroup: {
				name: "",
				id: "",
			},
			breadList: [],
			form: {
				startTime: "",
				endTime: "",
			},
			allGroup: [],
			picker_disabled: {
				disabledDate: (time) => {
					return time.getTime() > Date.now();
				},
			},
			chartSourceData: {
				data: [],
				dataX: [],
				dataY: [],
			},
			chartInstance: null,
			startIndex: 0, // 图表初始显示0,
			endIndex: 19, //最多显示20条 index为 20-1
		};
	},
	mounted() {
		this.initChart([]);
		this.fetchDate();
		this.getTopGroups();
		document.onkeydown = (e) => {
			if (e.keyCode === 13) {
				this.searchData();
			}
		};
	},
	methods: {
		...mapActions(["get_allGroups", "get_LoginLogsPercent", "get_GroupsChild"]),
		// 获取所有用户组
		getTopGroups() {
			this.get_allGroups().then((res) => {
				if (res && res.length > 0) {
					this.allGroup = res;
					groupFilter.initData(res);
					let reslut = groupFilter.getNodesByTreeLeval(1);
					if (reslut.length > 0) {
						this.currentGroup.name = "全部";
						this.currentGroup.id = null;
						this.breadList.push({
							id: this.currentGroup.id,
							name: this.currentGroup.name
						});
						this.get_LoginLogsPercentData(
							reslut.map(node => {
								return node.id;
							})
						);
					}
				}
			});
		},
		searchData() {
      this.tableData = [];
			this.initChart([]);
			this.getGroupIds(this.currentGroup.id);
		},
		getGroupIds(groupId) {
			let groupsIds = [];
			if (groupId === null) {
				groupsIds = groupFilter
					.getNodesByTreeLeval(1)
					.map(childNode => {
						return childNode.id;
					});
			} else {
				groupsIds = groupFilter
					.getChildNodesById(groupId)
					.map(childNode => {
						return childNode.id;
					});
			}
			if (groupsIds.length > 0) {
        const isPush = this.breadList.map((item) => {
          return item.id;
        });
        if(!isPush.includes(this.currentGroup.id)) {
          this.breadList.push({
            id: this.currentGroup.id,
            name: this.currentGroup.name
          });
        }

				this.get_LoginLogsPercentData(groupsIds);
			}else {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "无下级机构",
				});
        this.currentGroup = JSON.parse(JSON.stringify(this.breadList.at(-1)))
			}
		},
		// 获取登录率数据
		get_LoginLogsPercentData(ids) {
			let isFull = this.timeIsFull();
			if (!isFull) {
				return;
			}
			let param = {
				groupIds: ids.toString(),
				startTime: this.form.startTime
					? this.form.startTime + " " + "00:00:01"
					: "",
				endTime: this.form.endTime ? this.form.endTime + " " + "23:59:59" : "",
			};
			this.get_LoginLogsPercent(param).then((res) => {
				if (res) {
					this.labelTitle = this.currentGroup.name
					res = res.sort((next, prev) => {
						return next.groupId - prev.groupId;
					});
					this.initChart(res);
				}
			});
		},
		// 获取数据后第一次初始化图表
		initChart(data) {
			// 每次重置数据显示问题
			this.startIndex = 0;
			this.endIndex = 19;
			var dataY = [],
				dataX = [],
				that = this;
			if (data) {
				dataY = data.map((ele) => {
					return (ele.loginRat * 100).toFixed(2);
				});
				dataX = data.map((ele) => {
					return ele.groupName;
				});
			} else {
				dataY = [];
				dataX = [];
			}
			that.chartSourceData = {
				data: data,
				dataX: dataX,
				dataY: dataY,
			};
			this.setChartsData(dataX, dataY, data);
		},
		// 每次切换页码重新设置图表
		setChartsData(dataX, dataY, data) {
			let that = this;
			this.chartInstance = this.$echarts.init(
				document.getElementById("cylinderChart")
			);
			this.chartInstance.clear();
			this.chartInstance.off("click");
			this.chartInstance.setOption({
				dataZoom: [
					{
						type: "slider",
						show: false,
						startValue: this.startIndex,
						endValue: this.endIndex,
						xAxisIndex: [0],
						zoomLock: true,
					},
				],
				// title: {
				// 	show: false,
				// 	text: `${this.breadList[this.breadList.length > 0 ? (this.breadList.length - 1) : 0]?.name}登录率`,
				// 	x: "center",
				// },
				color: ["#5899DA"],
				tooltip: {
					trigger: "axis",
					className: "echartooltip",
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
						shadowStyle: {
							color: "rgba(235, 245, 255, 0.4)",
						},
					},
					formatter: function (params) {
						let dir = "";
						let tooltipData = data.filter((ele, idx) => {
							if (ele.groupName === params[0].axisValue) {
								if (idx !== data.length - 1) {
									dir =
										"position: absolute; top: calc(50% - 8px); left: -16px;border-style: solid; border-width: 8px 8px 8px 8px;border-color: transparent #fff transparent transparent;";
								} else {
									dir =
										"position: absolute; top: calc(50% - 8px); right: -16px;border-style: solid; border-width: 8px 8px 8px 8px;border-color: transparent transparent transparent #fff;";
								}
								return ele;
							}
						})[0];
						return `<div class="tooltip" style="width: 210px;height: 180px; display: block;position: relative;">
                                    <div style="${dir}"></div>
                                    <ul>
                                        <li style="padding: 6px 10px;font-size: 14px;display: flex;flex-direction: row;">
                                            <div style="width: 45%;">名称: </div>
                                            <div style="width: 50%; text-align: left;">${
																							tooltipData.groupName
																						}</div>
                                        </li>
                                        <li style="padding: 6px 10px;font-size: 14px;display: flex;flex-direction: row;">
                                            <div style="width: 45%;">登录率: </div>
                                            <div style="width: 50%; text-align: left;">${(
																							tooltipData.loginRat * 100
																						).toFixed(2)}%</div>
                                        </li>
                                        <li style="padding: 6px 10px;font-size: 14px;display: flex;flex-direction: row;">
                                            <div style="width: 45%;">总帐户数: </div>
                                            <div style="width: 50%;"> ${
																							tooltipData.accountNum
																						}</div>
                                        </li>
                                        <li style="padding: 6px 10px;font-size: 14px;display: flex;flex-direction: row;">
                                            <div style="width: 45%;">登录次数: </div>
                                            <div style="width: 50%;">${
																							tooltipData.loginNum
																						}</div>
                                        </li>
                                        <li style="padding: 6px 10px;font-size: 14px;display: flex;flex-direction: row;">
                                            <div style="width: 45%;">登录账户数: </div>
                                            <div style="width: 50%;">${
																							tooltipData.loginAccountNum
																						}</div>
                                        </li>
                                    </ul>
                                </div>`;
					},
				},
				grid: {
					left: "3%",
					right: "4%",
					bottom: "3%",
					containLabel: true,
				},
				xAxis: [
					{
						type: "category",
						data: dataX,
						axisTick: {
							alignWithLabel: true,
						},
						axisLine: {
							lineStyle: {
								color: "#E5E5E5",
							},
						},
						splitLine: {
							lineStyle: {
								color: "#E5E5E5",
							},
						},

						axisLabel: {
							fontSize: 15,
							color: "#182A4E",
							rotate: dataX.length >= 6 ? "40" : "0",
							// formatter: function (value) {
							//     if(dataX.length >= 4) {
							//         //x轴的文字改为竖版显示
							//         var str = value.split("");
							//         return str.join("\n");
							//     }else {
							//         return value
							//     }
							// }
						},
					},
				],
				yAxis: [
					{
						type: "value",
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						name: "",
						type: "bar",
						label: {
							show: true,
							position: "top",
							rotate: dataX.length >= 6 ? "40" : "0",
							formatter: function (params) {
								if (dataY[params.dataIndex] > 0) {
									return dataY[params.dataIndex] + "%";
								} else {
									return "";
								}
							},
							fontSize: 14,
							fontWeight: 400,
							color: "#182A4E",
							distance: 25,
						},
						data: dataY,
					},
				],
			});
			this.chartInstance.on("click", function (params) {
				if (params.seriesType === "bar") {
					let clickData = data.filter((ele) => {
						if (ele.groupName === params.name) {
							return ele;
						}
					});
					let filterData = that.breadList.filter((ele) => {
						if (ele.name === clickData[0].groupName) {
							return ele;
						}
					});
					if (filterData.length <= 0) {
						that.currentGroup.name = clickData[0].groupName;
						that.currentGroup.id = clickData[0].groupId;
						that.getGroupIds(that.currentGroup.id);
					}
				}
			});
			window.addEventListener("resize", () => {
				this.chartInstance.resize();
			});
		},
		// 切换页码
		pageChage(type) {
			if (type === "pre") {
				if (this.startIndex - 20 <= 0) {
					this.startIndex = 0;
					this.endIndex = 19;
				} else {
					this.startIndex = this.startIndex -= 20;
					this.endIndex = this.endIndex -= 20;
				}
				this.chartInstance.dispatchAction({
					type: "dataZoom",

					// 开始位置的数值
					startValue: this.startIndex,
					// 结束位置的数值
					endValue: this.endIndex,
				});
			} else if (type === "next") {
				if (this.endIndex + 20 >= this.chartSourceData.data.length) {
					this.startIndex = this.chartSourceData.data.length - 20;
					this.endIndex = this.chartSourceData.data.length;
				} else {
					this.startIndex = this.startIndex += 20;
					this.endIndex = this.endIndex += 20;
				}

				this.chartInstance.dispatchAction({
					type: "dataZoom",

					// 开始位置的数值
					startValue: this.startIndex,
					// 结束位置的数值
					endValue: this.endIndex,
				});
			}
		},
		// 切换页码，数据分页
		changePageSetChart(dataX, dataY, data) {
			let start = (this.chartPage.pageNumber - 1) * this.chartPage.pageSize;
			if (start >= this.chartSourceData.dataX.length) start = 0;
			let end = this.chartPage.pageNumber * this.chartPage.pageSize;
			if (end >= this.chartSourceData.dataX.length)
				end = this.chartSourceData.dataX.length;
			dataX = dataX.slice(start, end);
			dataY = dataY.slice(start, end);
			this.setChartsData(dataX, dataY, data);
		},
		// 选择时间范围
		changeTime(flag) {
			// flag为true时代表起始时间的选择
			// flag为false时代表结束时间的选择
			if (flag) {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			} else {
				if (this.form.startTime && this.form.endTime) {
					this.checkTime(flag, this.form.startTime, this.form.endTime);
				}
			}
		},
		// 校验选择的时间是否符合规范
		checkTime(flag, start, end) {
			if (flag) {
				if (new Date(start) > new Date(end)) {
					this.form.startTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "开始时间大于结束时间",
					});
				}
			} else {
				if (new Date(start) > new Date(end)) {
					this.form.endTime = null;
					this.$message({
						dangerouslyUseHTMLString: true,
						duration: 1500,
						customClass: "scenator_briefMsg warn",
						message: "结束时间小于开始时间",
					});
				}
			}
		},
		// 判断时间条件是否完整
		timeIsFull() {
			if (this.form.startTime && this.form.endTime) {
				return true;
			} else if (this.form.startTime && !this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "结束时间不能为空",
				});
				return false;
			} else if (!this.form.startTime && this.form.endTime) {
				this.$message({
					dangerouslyUseHTMLString: true,
					duration: 1500,
					customClass: "scenator_briefMsg warn",
					message: "开始时间不能为空",
				});
				return false;
			} else {
				return true;
			}
		},
		changeTitle(item, idx) {
			this.timeRang = [];
			if (idx < this.breadList.length) {
				this.breadList = this.breadList.slice(0, idx);
				this.currentGroup.name = item.name;
				this.currentGroup.id = item.id;
				this.getGroupIds(this.currentGroup.id);
			}
		},
		// 设置默认日期
		async fetchDate() {
			var now = new Date();
			var year = now.getFullYear();
			var month = now.getMonth() + 1;
			var day = now.getDate();
			var hour = now.getHours();
			var min = now.getMinutes();
			var seconds = now.getSeconds();
			const after =
				year +
				"-" +
				(month < 10 ? "0" + month : month) +
				"-" +
				(day < 10 ? "0" + day : day);
			const before = await this.getBeforeDate(Date.now());

			this.form.startTime = before;
			this.form.endTime = after;
		},
		// 获取当前日期30天前的日期
		getBeforeDate(temp) {
			return new Promise((resove, reject) => {
				var timestr = new Date(temp - 60 * 60 * 24 * 30 * 1000);

				var year = timestr.getFullYear();

				var month = timestr.getMonth() + 1;

				var date = timestr.getDate();

				var hour = timestr.getHours();

				var minute = timestr.getMinutes();

				var second = timestr.getSeconds();

				var datetime =
					year +
					"-" +
					(month < 10 ? "0" + month : month) +
					"-" +
					(date < 10 ? "0" + date : date);

				resove(datetime);
			});
		},
	},
};
</script>

<style lang="less" scoped>
.bread {
	height: 60px;
	line-height: 18px;
	border-bottom: 1px solid #ccc;
	padding: 0 36px;
	overflow-x: auto;
	display: flex;
	.breadList {
		display: flex;
		flex-direction: row;
		align-items: center;
		.breadItem {
			padding: 0 2px 0 0;
			span {
				margin-right: 4px;
				cursor: pointer;
				font-size: 16px;
				font-weight: 500;
			}
			.active {
				color: rgba(0, 0, 0, 0.6);
			}
		}
	}
}
.lpHeader {
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 100%;
	justify-content: flex-start;
	margin: 24px 0 0 25px;
	.filterItem {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		margin-right: 8px;
		.time-splite {
			margin: 0 4px;
		}
		span {
			font-size: 14px;
			margin-right: 4px;
			font-weight: 500;
			white-space: nowrap;
		}
	}
}
.filter-tips {
	padding: 10px 0 0 25px;
	color: #5d697a;
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 14px;
}
.echarts {
	// width: 1240px;
	width: 72%;
	height: 600px;
	margin: 80px auto 0;
	.chart_title {
		margin-top: 50px;
		text-align: center;
		font-size: 19px;
		font-weight: 400;
		color: #5d697a;
	}
	.chart_wrapper {
		width: 100%;
		height: 65%;
		position: relative;
		.pr_item {
			position: absolute;
			top: 0;
			left: 70px;
			height: 100%;
			width: 30px;
			i {
				transition: all 0.3s;
				opacity: 0;
				cursor: pointer;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				font-size: 50px;
				color: #5d697a;
			}
		}
		.next_item {
			position: absolute;
			top: 0;
			right: 70px;
			height: 100%;
			width: 30px;
			i {
				opacity: 0;
				transition: all 0.3s;
				cursor: pointer;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				font-size: 50px;
				color: #5d697a;
			}
		}
	}
	.chart_wrapper:hover {
		.pr_item i {
			opacity: 1;
		}
		.next_item i {
			opacity: 1;
		}
	}
	#cylinderChart {
		width: 80%;
		height: 100%;
		margin: 0 auto;
		position: absolute;
		left: 50%;
		top: 0;
		transform: translateX(-50%);
		.echartooltip {
			border-width: 0 !important;
			padding: 0 !important;
			border-style: none !important;
		}
	}
}
</style>
