{"_from": "jian-pinyin", "_id": "jian-pinyin@0.2.3", "_inBundle": false, "_integrity": "sha512-FjE1EaE3A+/xtsJ+UyOma7dqYzuqWsM4AOlFO9TMHq/5KjQcwiMbntCCp7A4Crta7vJC4+TC2thM+ry2RNL6UQ==", "_location": "/jian-pinyin", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "jian-pinyin", "name": "jian-pinyin", "escapedName": "jian-pinyin", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/jian-pinyin/-/jian-pinyin-0.2.3.tgz", "_shasum": "1fc6bff7088ac388283f31a6fd4481169f85e0e2", "_spec": "jian-pinyin", "_where": "E:\\WorkSpace\\App\\ControlLogs\\branches\\1.1.0-外链完整版", "bugs": {"url": "https://github.com/xinglie/vue/issues"}, "bundleDependencies": false, "deprecated": false, "description": "包含20870个汉字的拼音，完整多音字，无第三方依赖，压缩前仅72KB", "files": ["index.js", "lc.js", "main.js"], "homepage": "https://github.com/xinglie/pinyin#readme", "keywords": ["pinyin", "拼音"], "license": "MIT", "main": "main.js", "name": "jian-pinyin", "version": "0.2.3"}