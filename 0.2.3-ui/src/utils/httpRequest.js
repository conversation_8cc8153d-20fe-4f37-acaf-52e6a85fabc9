/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-01-21 16:49:26
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-07 10:53:01
 */
// 导入axios
import axios from "axios";

// 公共路由(网络请求地址)
// axios.defaults.baseURL = "http://localhost:8080";
// 请求响应超时时间
axios.defaults.timeout = 15000;

// document.cookie = "FULONGTECH_SESSION=d1d79cbf-6dad-46bd-94e0-f67b9e49b520";

// 封装自己的get/post方法
export default {
	get: function (path = "", data = {}, options = {}) {
		return new Promise(function (resolve, reject) {
			axios
				.get(path, {
					params: data,
					...options,
				})
				.then(function (response) {
					// 按需求来，这里我需要的是response，所以返回response，一般直接返回response
					resolve(response);
				})
				.catch(function (error) {
					reject(error);
				});
		});
	},
	post: function (path = "", data = {}) {
		return new Promise(function (resolve, reject) {
			axios
				.post(path, data)
				.then(function (response) {
					resolve(response);
				})
				.catch(function (error) {
					reject(error);
				});
		});
	},
};
