/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-05-18 16:14:09
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-14 16:43:55
 */
import _ from "lodash";
class GroupFilter {
	constructor() {
		this.allGroup = [];
		this.treeData = [];
	}

	initData(data) {
		this.allGroup = data.map(node => {
			if (node.id === node.parentId) {
				node.parentId = null;
			}
			return node;
		});
		this.treeData = this.createTreeData(this.allGroup);
		return this.treeData;
	}

	/**
	 * @descripton: 过滤出当前数据对应的二三级机构
	 * @param undefined
	 * @return {*}
	 * @author: Gary
	 */
	filter(groupIds) {
		let result = [];
		let treeData = [...this.treeData];
		groupIds.forEach(item => {
			if (!item.sysGroup) {
				treeData.forEach(nodes => {
					let tt = this.recursiveQuery(item.id, nodes.children);
					if (tt.length > 0) {
						result.push(tt);
					}
				});
			}
		});
		result.sort(function(a, b) {
			return b.length - a.length;
		});
		return result;
	}

	/**
	 * @descripton: 递归查询二三级
	 * @return {*}
	 * @author: Gary
	 */
	recursiveQuery(id, treeData) {
		let result = [],
			_this = this;
		function temp(id, treeData) {
			treeData.forEach(item => {
				if (id === item.id) {
					if (item.treeLeval === 3) {
						let parent = _.filter(_this.allGroup, [
							"id",
							item.parentId
						]);
						result.push(...parent, item);
					}
					if (item.treeLeval === 2) {
						result.push(item);
					}
					if (item.treeLeval > 3) {
						let parents = _this.searchDeepLevel(item);
						result.push(...parents);
					}
				}
				if (item.children) {
					temp(id, item.children);
				}
			});
		}
		temp(id, treeData);
		return result;
	}

	/**
	 * @descripton: 查找大于3级用户组的二三级用户组名称
	 * @return {*}
	 * @author: Gary
	 */
	searchDeepLevel(item) {
		let result = [];
		let _this = this;
		function searchDeep(allGroup, Id) {
			return allGroup
				.filter(group => {
					if (group.id === Id) {
						return group;
					}
				})
				.map(item => {
					if (item.treeLeval === 2 || item.treeLeval === 3) {
						result.push(item);
					}
					let parent = searchDeep(_this.allGroup, item.parentId);
					return parent;
				});
		}
		searchDeep(this.allGroup, item.id);
		return result;
	}

	/**
	 * @descripton: 将用户组集合转化为树结构
	 * @param undefined
	 * @return {*}
	 * @author: Gary
	 */
	createTreeData(treeNodes, pid, treeLeval) {
		return treeNodes
			.filter(item => {
				// 如果没有父id（第一次递归的时候）将所有父级查询出来
				if (
					pid === undefined
						? !item.parentId
						: item.parentId === pid
				) {
					if (treeLeval === undefined) {
						item.treeLeval = 1;
					} else {
						item.treeLeval = treeLeval + 1;
					}
					_.forEach(this.allGroup, function(val) {
						if (val.id === item.id) {
							val.treeLeval = item.treeLeval;
						}
					});
					return item;
				}
			})
			.map(item => {
				// 通过父节点ID查询所有子节点
				item.children = this.createTreeData(
					treeNodes,
					item.id,
					item.treeLeval
				);
				return item;
			});
	}

	/**
	 * @descripton: 根据树节点层级筛选数据
	 * @return {*}
	 * @author: Gary
	 */
	getNodesByTreeLeval(leval) {
		let result = _.filter(this.allGroup, function(item) {
			if (item.treeLeval === leval) {
				return item;
			}
		});
		return result;
	}

	/**
	 * @descripton: 根据Id获取对应用户组的子用户组
	 * @param undefined
	 * @return {*}
	 * @author: Gary
	 */
	getChildNodesById(Id) {
		let result = _.filter(this.allGroup, function(item) {
			if (item.parentId === Id) {
				return item;
			}
		});
		return result;
	}

	/**
	 * @descripton: 根据二级机构Id获取当前用户组下的所有子用户组
	 * @param undefined
	 * @return {*}
	 * @author: Gary
	 */
	getAllChildNodesById(Id) {
		let result = [];
		let _this = this;
		function getNodes(allGroup, Id) {
			return allGroup
				.filter(group => {
					if (group.parentId === Id) {
						return group;
					}
				})
				.map(item => {
					result.push(item);
					let child = getNodes(_this.allGroup, item.id);
					return child;
				});
		}
		getNodes(this.allGroup, Id);
		return result;
	}
}

export default GroupFilter;
