{"zh_CN": {"Layout": {"menu_nav": {"menu_main_title": "Log management", "menu_item_list": {"item_1": "Operation log", "item_2": "Login rate", "item_3": "Visit rate"}}}, "ControlLogs": {"tab_list": {"tab_1": "<PERSON><PERSON>", "tab_2": "Operation", "tab_3": "Visit"}}, "ContrlCard": {"form_labels": {"label_1": "Operator", "label_2": "Secondary institution", "label_3": "Tertiary institution", "label_4": "Time range"}, "placeholder": {"place_1": "Please Select the operator", "place_2": "All institutions", "place_3": "All institutions", "place_4": "Search", "place_5": "Please select secondary institutions", "place_6": "Start time", "place_7": "End time"}, "ip_address": "IP Address", "btn_1": "Search", "btn_2": "Reset", "btn_3": "Export", "empty": "No result found"}, "ChangeCard": {"form_labels": {"label_1": "Operator", "label_2": "Secondary institution", "label_3": "Tertiary institution", "label_4": "Unit name", "label_5": "Operation type", "label_6": "Time range"}, "placeholder": {"place_1": "Please Select the operator", "place_2": "All institutions", "place_3": "All Units", "place_4": "Please select operation type", "place_5": "Please select secondary institutions", "place_6": "Start time", "place_7": "End time", "place_8": "Search"}, "btn_1": "Search", "btn_2": "Reset", "btn_3": "Export", "empty": "No result found.", "operation_time": "Time", "ip_address": "IP Address"}, "VisitCard": {"form_labels": {"label_1": "Operator", "label_2": "Secondary institution", "label_3": "Tertiary institution", "label_4": "Access content", "label_5": "Time range"}, "placeholder": {"place_1": "Please Select the operator", "place_2": "All institutions", "place_3": "Please select module", "place_4": "Please select secondary institutions first", "place_5": "Start time", "place_6": "End time", "place_7": "Search"}, "operation_time": "Time", "ip_address": "IP Address", "btn_1": "Search", "btn_2": "Reset", "btn_3": "Export", "empty": "No search results found!", "visitor_logBehavior": "Operation records", "detail_info": "details"}, "LoginPercent": {"form_labels": {"label_1": "Time range"}, "placeholder": {"place_1": "Start time", "place_2": "End time"}, "btn_1": "Search", "title": "Login rate", "tips": "If the time range is not specified, the data of the last month is calculated by default!"}, "VisitPerCard": {"form_labels": {"label_1": "Secondary institution", "label_2": "Tertiary institution", "label_3": "Time range"}, "placeholder": {"place_1": "All institutions", "place_2": "Please select secondary institutions first", "place_3": "Start time", "place_4": "End time", "place_5": "Search"}, "feature_name": "Module name", "account_number": "Number of all accounts", "visitor_number": "Number of visitors", "visit_count_number": "Number of visits", "visit_rate": "Visit rate(%)", "btn_1": "Search", "btn_2": "Reset", "empty": "No result found."}}}