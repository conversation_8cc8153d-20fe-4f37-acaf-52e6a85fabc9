<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icon/向左 2</title>
    <g id="tab" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Tab" transform="translate(-580.000000, -168.000000)">
            <g id="编组-5" transform="translate(67.000000, 158.000000)">
                <g id="编组-3" transform="translate(506.000000, 0.000000)">
                    <g id="Icon/向左-2" transform="translate(7.000000, 10.000000)">
                        <g id="Icon/向左" transform="translate(10.000000, 10.000000) rotate(-180.000000) translate(-10.000000, -10.000000) ">
                            <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                            <g id="左" transform="translate(10.000000, 10.000000) rotate(-180.000000) translate(-10.000000, -10.000000) translate(2.000000, 2.000000)">
                                <rect id="矩形" fill-opacity="0.01" fill="#FFFFFF" fill-rule="nonzero" x="0" y="0" width="16" height="16"></rect>
                                <path d="M8,14.6666667 C11.6819,14.6666667 14.6666667,11.6819 14.6666667,8 C14.6666667,4.3181 11.6819,1.33333333 8,1.33333333 C4.3181,1.33333333 1.33333333,4.3181 1.33333333,8 C1.33333333,11.6819 4.3181,14.6666667 8,14.6666667 Z" id="路径" stroke="#0854A1" stroke-linejoin="round"></path>
                                <polyline id="路径" stroke="#0854A1" stroke-linejoin="round" points="7 11 10 8 7 5"></polyline>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>