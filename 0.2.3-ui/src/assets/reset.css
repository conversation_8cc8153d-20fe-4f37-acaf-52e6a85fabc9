* {
    margin: 0;
    padding: 0;
}
html, body {
    width: 100%;
    height: 100%;
    /* min-width: 1600px; */
    /* overflow-x: auto; */
}
ul li {
    list-style: none;
}
.el-scrollbar__view {
    padding-top: 0 !important;
}
.is-horizontal {
  display: none;
}
.controlLogs_selectPopper .el-scrollbar {
  display: block !important;
}


/* scenator common style */
.circulArarrowRightIcon,
.scenatorStyle.el-tabs--card .el-icon-arrow-left,
.scenatorStyle.el-tabs--card .el-icon-arrow-right {
  background: url("./scenator_style/scenator_circulArarrowRight.svg") no-repeat center;
  background-size: 100% 100%;
}

.circulArarrowRightActiveIcon,
.scenatorStyle.el-tabs--card .el-icon-arrow-right:active,
.scenatorStyle.el-tabs--card .el-icon-arrow-left:active {
  background: url("./scenator_style/scenator_circulArarrowRight_active.svg") no-repeat center;
  background-size: 100% 100%;
}

.circulArarrowLeftIcon,
.scenatorStyle.el-tabs--card .el-icon-arrow-left {
  background: url("./scenator_style/scenator_circulArarrowLeft.svg") no-repeat center;
  background-size: 100% 100%;
}

.circulArarrowLeftActiveIcon,
.scenatorStyle.el-tabs--card .el-icon-arrow-left:active {
  background: url("./scenator_style/scenator_circulArarrowLeft_active.svg") no-repeat center;
  background-size: 100% 100%;
}

.circulAddIcon,
.scenatorStyle.el-tabs--card .el-icon-plus {
  background: url("./scenator_style/scenator_circulAdd.svg") no-repeat center;
  background-size: 100% 100%;
}

.circulAddActiveIcon,
.scenatorStyle.el-tabs--card .el-icon-plus:active {
  background: url("./scenator_style/scenator_circulAdd_active.svg") no-repeat center;
  background-size: 100% 100%;
}

.removeIcon,
.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__close,
.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close {
  background: url("./scenator_style/scenator_removeIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.removeActiveIcon,
.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__close:active,
.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:active {
  background: url("./scenator_style/scenator_removeActiveIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenatorStyle.el-button {
  padding: 9px 16px;
  background: rgba(0, 0, 0, 0);
  border: 1px solid rgba(0, 0, 0, 0);
  color: #0854A1;
  border-radius: 2px;
  font-size: 14px;
}

.scenatorStyle.el-button.less3word > span {
  padding-left: 2px;
  padding-right: 2px;
}

.scenatorStyle.el-button.smallButton {
  padding: 7px 12px;
}

.scenatorStyle.el-button .el-message-box__btns button:nth-child(2) {
  margin-left: 16px;
}

.scenatorStyle.el-button.el-button--main--button {
  background: #0854A1;
  border-color: #0854A1;
  color: #fff;
}

.scenatorStyle.el-button.el-button--main--button:hover,
.scenatorStyle.el-button.el-button--main--button:focus {
  background: #114171;
  border-color: #114171;
  color: #fff;
}

.scenatorStyle.el-button.el-button--main--button:active {
  background: #114171;
  border-color: #114171;
  color: #fff;
}

.scenatorStyle.el-button.el-button--main--button.is-disabled,
.scenatorStyle.el-button.el-button--main--button.is-disabled:hover,
.scenatorStyle.el-button.el-button--main--button.is-disabled:focus {
  color: #fff;
  background-color: #9DBBDA;
  border-color: #9DBBDA;
}

.scenatorStyle.el-button.el-button--secondary--button {
  background: #fff;
  border-color: #0854A1;
  color: #0854A1;
}

.scenatorStyle.el-button.el-button--secondary--button.transparentBackground {
  background-color: rgba(0, 0, 0, 0);
}

.scenatorStyle.el-button.el-button--secondary--button:hover,
.scenatorStyle.el-button.el-button--secondary--button:focus {
  background: #EBF5FE;
  border-color: #114171;
  color: #0854A1;
}

.scenatorStyle.el-button.el-button--secondary--button:active {
  background: #0854A1;
  border-color: #114171;
  color: #fff;
}

.scenatorStyle.el-button.el-button--secondary--button.is-disabled,
.scenatorStyle.el-button.el-button--secondary--button.is-disabled:hover,
.scenatorStyle.el-button.el-button--secondary--button.is-disabled:focus {
  color: #9DBBDA;
  background-color: #fff;
  border-color: #9DBBDA;
}

.scenatorStyle.el-button.el-button--secondary--button.is-disabled.transparentBackground,
.scenatorStyle.el-button.el-button--secondary--button.is-disabled.transparentBackground:hover,
.scenatorStyle.el-button.el-button--secondary--button.is-disabled.transparentBackground:focus {
  background-color: rgba(0, 0, 0, 0);
}

.scenatorStyle.el-button.el-button--third--button:hover,
.scenatorStyle.el-button.el-button--third--button:focus {
  background: #EBF5FE;
  border-color: #0854A1;
  color: #0854A1;
}

.scenatorStyle.el-button.el-button--third--button:active {
  border-color: #114171;
  color: #fff;
  background-color: #114171;
}

.scenatorStyle.el-button.el-button--third--button.is-disabled,
.scenatorStyle.el-button.el-button--third--button.is-disabled:hover,
.scenatorStyle.el-button.el-button--third--button.is-disabled:focus,
.scenatorStyle.el-button.el-button--third--button.is-disabled:active {
  background: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  color: #9DBBDA;
}

.scenatorStyle.el-radio {
  border-color: #A7ADB4;
}

.scenatorStyle.el-radio .el-radio__inner {
  border: 1px solid #A7ADB4;
  width: 16px;
  height: 16px;
  background-color: #fff;
}

.scenatorStyle.el-radio .el-radio__inner::after {
  background-color: #0854A1;
  width: 6px;
  height: 6px;
}

.scenatorStyle.el-radio .el-radio__label {
  color: #182A4E;
}

.scenatorStyle.el-radio.is-checked .el-radio__label {
  color: #182A4E;
}

.scenatorStyle.el-radio.is-checked .el-radio__inner {
  border: 1px solid #A7ADB4;
  background-color: #fff;
}

.scenatorStyle.el-radio.is-disabled .el-radio__input.is-disabled .el-radio__inner,
.scenatorStyle.el-radio.is-disabled .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #F8F8F8;
  border-color: #D5D8DC;
}

.scenatorStyle.el-radio.is-disabled .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #9DBBDA;
}

.scenatorStyle.el-radio[scenatorType=readOnly] .el-radio__input.is-disabled .el-radio__inner,
.scenatorStyle.el-radio[scenatorType=readOnly] .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #F8F8F8;
  border-color: #A7ADB4;
}

.scenatorStyle.el-radio[scenatorType=readOnly] .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #6A6D70;
}

.scenatorStyle.el-radio:hover .el-radio__inner {
  border-color: #0854A1;
}

.scenatorStyle.el-checkbox {
  color: #182A4E;
  font-size: 14px;
}

.scenatorStyle.el-checkbox .el-checkbox__inner {
  border: 1px solid #A7ADB4;
  width: 16px;
  height: 16px;
}

.scenatorStyle.el-checkbox .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #182A4E;
}

.scenatorStyle.el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner,
.scenatorStyle.el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: #fff;
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox .el-checkbox__inner::after {
  border-color: #0854A1;
  top: 2px;
  left: 5px;
}

.scenatorStyle.el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: #fff;
}

.scenatorStyle.el-checkbox .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  background-color: #0854A1;
  top: 3px;
  left: 3px;
  height: 8px;
  width: 8px;
  transform: rotate(0) scaleY(1);
}

.scenatorStyle.el-checkbox:hover .el-checkbox__inner,
.scenatorStyle.el-checkbox.is-checked:hover .el-checkbox__inner,
.scenatorStyle.el-checkbox:hover .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-checkbox[scenatortype=readOnly]:hover .el-checkbox__inner {
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox.is-disabled:hover .el-checkbox__inner {
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #A3AAB9;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-disabled .el-checkbox__inner {
  background: #F8F8F8;
  border-color: #D5D8DC;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  border-color: #D5D8DC;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #9DBBDA;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
  border-color: #D5D8DC;
}

.scenatorStyle.el-checkbox.is-disabled .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  background-color: #9DBBDA;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #182A4E;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-disabled .el-checkbox__inner {
  background: #F8F8F8;
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #6A6D70;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
  border-color: #A7ADB4;
}

.scenatorStyle.el-checkbox[scenatorType=readOnly].is-disabled .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  background-color: #6A6D70;
}

.scenatorStyle.el-input .el-input__inner {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #A7ADB4;
  height: 32px;
  padding: 0 10px;
  color: #182A4E;
}

.scenatorStyle.el-input .el-input__inner:hover {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-input .el-input__inner::-webkit-input-placeholder {
  color: #6A6D70;
}

.scenatorStyle.el-input .el-input__inner::-ms-input-placeholder {
  color: #6A6D70;
}

.scenatorStyle.el-input .el-input__inner[disabled=disabled] {
  border: 1px solid #D5D8DC;
  background-color: #F8F8F8;
}

.scenatorStyle.el-input .el-input__inner[disabled=disabled]:hover {
  border: 1px solid #D5D8DC;
}

.scenatorStyle.el-input .el-input__inner[disabled=disabled][scenatorType=readOnly] {
  border: 1px solid #A7ADB4;
}

.scenatorStyle.el-input .el-input__inner[disabled=disabled][scenatorType=readOnly]:hover {
  border: 1px solid #A7ADB4;
}

.scenatorStyle.el-input.is-disabled .el-input__inner {
  color: #A3AAB9;
}

.scenatorStyle.el-input.is-disabled .el-input__inner[scenatorType=readOnly] {
  color: #182A4E;
}

.scenatorStyle.el-input.is-active .el-input__inner,
.scenatorStyle.el-input .el-input__inner:focus {
  border-color: #0854A1;
}

.scenatorStyle.el-select .el-input .el-select__caret {
  transform: none;
  transition: none;
}

.scenatorStyle.el-select .el-input__inner:focus {
  border-color: #A7ADB4;
}

.scenatorStyle.el-select .el-input__inner {
  background-color: #fff !important;
  border-radius: 2px;
  border: 1px solid #A7ADB4;
  height: 32px;
  padding: 0 10px;
  padding-right: 0px;
  color: #182A4E;
}

.scenatorStyle.el-select .el-input__inner:hover {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-select .el-input__icon {
  /* line-height: 100%; */
  height: 100%;
  color: #0854A1;
  font-weight: 900;
  width: 32px;
}

.scenatorStyle.el-select .el-icon-arrow-up:before {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  position: absolute;
  top: 7px;
  left: 8px;
}

.scenatorStyle.el-select .el-input__suffix {
  right: 0;
  box-sizing: border-box;
  height: calc(100% - 2px);
  padding: 0px 0px 0px 0px;
  margin: 1px 1px 0px 0px;
  transform: scale(1);
}

.scenatorStyle.el-select:hover .el-input__suffix {
  background: #EBF5FE;
}

.scenatorStyle.el-select .el-input.is-focus .el-input__inner {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-select .el-input.is-focus .el-icon-arrow-up:before {
  background: url("./scenator_style/scenator_arrowUpActive.svg") no-repeat center;
  transform: rotateZ(180deg);
}

.scenatorStyle.el-select .el-input.is-focus .el-input__suffix {
  background: #0854A1;
}

.scenatorStyle.el-select .el-input__inner[disabled=disabled] {
  border: 1px solid #D5D8DC;
  color: #A3AAB9;
}

.scenatorStyle.el-select .el-input.is-disabled .el-input__inner:hover {
  border: 1px solid #D5D8DC;
}

.scenatorStyle.el-select .el-input.is-disabled:hover .el-input__suffix {
  background-color: rgba(0, 0, 0, 0);
}

.scenatorStyle.el-select .el-input.is-disabled .el-input__icon {
  opacity: 0.5;
}

.scenator_askIcon,
.el-message-box.scenator_confirmPrompt.ask .el-message-box__title::before {
  background: url("./scenator_style/scenator_askIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenator_successIcon,
.scenator_briefMsg.success .el-message__icon,
.scenator_complexMsg.success .el-icon-info,
.scenator_topMsg.success .el-icon-info {
  background: url("./scenator_style/scenator_successIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenator_warnIcon,
.scenator_briefMsg.warn .el-message__icon,
.scenator_complexMsg.warn .el-icon-info,
.scenator_topMsg.warn .el-icon-info,
.el-message-box.scenator_confirmPrompt.warn .el-message-box__title::before {
  background: url("./scenator_style/scenator_warnIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenator_infoIcon,
.scenator_briefMsg.info .el-message__icon,
.scenator_complexMsg.info .el-icon-info,
.scenator_topMsg.info .el-icon-info,
.el-message-box.scenator_confirmPrompt.info .el-message-box__title::before {
  background: url("./scenator_style/scenator_infoIcon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenator_errorIcon,
.scenator_briefMsg.error .el-message__icon,
.scenator_complexMsg.error .el-icon-info,
.scenator_topMsg.error .el-icon-info,
.el-message-box.scenator_confirmPrompt.error .el-message-box__title::before {
  background: url("./scenator_style/scenator_errorIncon.svg") no-repeat center;
  background-size: 100% 100%;
}

.scenator_briefMsg {
  min-width: 192px;
  max-width: 536px;
  background: #fff;
  padding: 14px 16px;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1), 0px 0px 0px 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  color: #182A4E;
  border: 0px;
}

.scenator_briefMsg article {
  color: #182A4E;
}

.scenator_briefMsg .el-message__icon {
  width: 15px;
  height: 15px;
  margin-right: 8px;
  position: absolute;
  top: 14px;
}

.scenator_briefMsg .el-message__content {
  line-height: 16px;
  width: calc(100% - 24px);
  margin-left: 24px;
}

.scenator_briefMsg .el-icon-info:before {
  content: "";
}

.scenator_complexMsg {
  padding: 8px 16px;
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  width: 720px;
  color: #182A4E;
}

.scenator_complexMsg article {
  color: #182A4E;
  display: inline-block;
  float: left;
  width: calc(100% - 190px);
}

.scenator_complexMsg .el-icon-info {
  width: 15px;
  height: 15px;
  margin-right: 8px;
  position: absolute;
}

.scenator_complexMsg .el-message__content {
  line-height: 30px;
  width: calc(100% - 24px);
  margin-left: 24px;
}

.scenator_complexMsg .el-icon-info:before {
  content: "";
}

.scenator_complexMsg.success {
  background: #F1FDF6;
  border: 1px solid rgba(16, 126, 62, 0.45);
}

.scenator_complexMsg.warn {
  background: #FEF7F0;
  border: 1px solid rgba(233, 115, 12, 0.45);
}

.scenator_complexMsg.info {
  background: #F5FAFF;
  border: 1px solid rgba(10, 110, 209, 0.45);
}

.scenator_complexMsg.error {
  background: #FEEFEF;
  border: 1px solid rgba(187, 0, 0, 0.45);
}

.scenator_topMsg {
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.12);
  border-radius: 0px;
  width: 100%;
  color: #182A4E;
  padding: 14px 16px;
  top: 0px !important;
  border: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scenator_topMsg article {
  color: #182A4E;
}

.scenator_topMsg .el-icon-info {
  width: 15px;
  height: 15px;
  margin-right: 8px;
}

.scenator_topMsg .el-icon-info:before {
  content: "";
}

.scenator_topMsg.success {
  background: #F1FDF6;
}

.scenator_topMsg.warn {
  background: #FEF7F0;
}

.scenator_topMsg.info {
  background: #F5FAFF;
}

.scenator_topMsg.error {
  background: #FEEFEF;
}

.el-message-box.scenator_confirmPrompt {
  box-shadow: inset 0px 1px 0px 0px #D9D9D9;
  background-color: #fff;
  border: 0;
  border-radius: 4px;
  padding: 0px;
}

.el-message-box.scenator_confirmPrompt .el-message-box__header {
  padding: 10px 16px 12px 16px;
  font-size: 14px;
  background-color: #fff;
}

.el-message-box.scenator_confirmPrompt .el-message-box__header .el-message-box__title {
  color: #182A4E;
  font-size: 16px;
  line-height: 100%;
  overflow: hidden;
}

.el-message-box.scenator_confirmPrompt .el-message-box__header .el-message-box__title::before {
  content: " ";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  margin-top: -2px;
}

.el-message-box.scenator_confirmPrompt .el-message-box__content {
  padding: 24px 16px;
  background-color: #fff;
  color: #182A4E;
}

.el-message-box.scenator_confirmPrompt .el-message-box__btns {
  padding: 8px 16px;
  border-top: 1px solid #D5D8DC;
  overflow: hidden;
}

.el-message-box.scenator_confirmPrompt .el-message-box__btns .el-button {
  float: right;
}

.el-message-box.scenator_confirmPrompt .el-message-box__btns .el-button:nth-child(1) {
  margin-left: 16px;
}

.el-message-box.scenator_confirmPrompt.ask .el-message-box__header {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #182A4E;
}

.el-message-box.scenator_confirmPrompt.warn .el-message-box__header {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #E9730C;
}

.el-message-box.scenator_confirmPrompt.info .el-message-box__header {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #0A6ED1;
}

.el-message-box.scenator_confirmPrompt.error .el-message-box__header {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15), inset 0px -1px 0px 0px #b00;
}

.el-message-box.scenator_confirmPrompt .scenator_confirmPrompt_article article {
  line-height: 28px;
  color: #182A4E;
}

.el-message-box.scenator_confirmPrompt .scenator_confirmPrompt_showMore {
  margin-top: 16px;
  color: #0854A1;
  cursor: pointer;
}

.scenatorStyle.el-dialog__wrapper {
  justify-content: center;
  align-items: center;
  display: flex;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header {
  padding: 10px 16px 12px 16px;
  background-color: #fff;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 1px 0px 0px #D9D9D9, 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__title {
  font-size: 16px;
  color: #182A4E;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: #0854A1;
  font-weight: 900;
  margin-top: 4px;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__close {
  width: 20px;
  height: 20px;
  border: 1px solid transparent;
  border-radius: 2px;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__close:hover {
  background-color: #EBF5FE;
  width: 20px;
  height: 20px;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-dialog__close:active {
  background-color: #0854A1;
  width: 20px;
  height: 20px;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__header .el-icon-close:before {
  content: " ";
}

.scenatorStyle.el-dialog__wrapper .el-dialog__headerbtn {
  position: initial;
}

.scenatorStyle.el-dialog__wrapper .el-dialog {
  margin: 0;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__footer {
  padding: 8px 16px;
  box-shadow: inset 0px 1px 0px 0px #D9D9D9;
}

.scenatorStyle.el-dialog__wrapper .el-dialog__footer .el-button:nth-child(1) {
  margin-right: 0px;
}

.scenator_loading {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.scenator_loading span {
  display: block;
  width: 10%;
  height: 100%;
  margin-right: 0px;
  border-radius: 50%;
  background: #0854A1;
  -webkit-animation: scenatorLoading 1.5s ease infinite;
}

.scenator_loading span:nth-child(1) {
  animation-delay: 0.5s;
}

.scenator_loading span:nth-child(2) {
  animation-delay: 0.9s;
}

.scenator_loading span:nth-child(3) {
  animation-delay: 1.2s;
}

@keyframes scenatorLoading {
  0% {
    opacity: 1;
    transform: scale(2.5);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.3);
  }
}
.scenatorStyle.el-date-editor {
  width: 200px;
  height: 32px;
}

.scenatorStyle.el-date-editor table {
  display: table;
}

.scenatorStyle.el-date-editor th,
.scenatorStyle.el-date-editor td {
  border: 0px;
}

.scenatorStyle.el-date-editor tr:nth-child(2n) {
  background-color: transparent;
}

.scenatorStyle.el-date-editor tr {
  border-top: 0px;
}

.scenatorStyle.el-date-editor .el-input__inner {
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #A7ADB4;
  height: 32px;
  color: #182A4E;
  padding-left: 35px;
}

.scenatorStyle.el-date-editor .el-input__inner:focus ~ .el-input__prefix {
  background: #0854A1;
}

.scenatorStyle.el-date-editor .el-input__inner:focus ~ .el-input__prefix .el-input__icon {
  color: white;
}

.scenatorStyle.el-date-editor .el-input__icon {
  line-height: 100%;
  color: #0854A1;
  font-weight: 550;
}

.scenatorStyle.el-date-editor .el-input__prefix {
  width: 30px;
  height: 30px;
  left: 1px;
  background: #fff;
  top: 1px;
}

.scenatorStyle.el-date-editor .el-input__prefix .el-icon-time:before {
  width: 14px;
  height: 16px;
}

.scenatorStyle.el-date-editor .el-icon-circle-close {
  display: flex;
  justify-content: center;
  align-items: center;
}

.scenatorStyle.el-date-editor .el-icon-circle-close:before {
  content: " ";
  background: url("./scenator_style/scenator_removeIcon.svg") no-repeat center;
  width: 16px;
  height: 16px;
  /* position: absolute; */
}

.scenatorStyle.el-date-editor .el-input__suffix {
  width: 25px;
  right: 5px;
}

.scenatorStyle.el-date-editor:hover .el-input__inner {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-date-editor:hover .el-input__prefix {
  background-color: #EBF5FE;
}

.scenatorStyle.el-date-editor:hover .el-input__prefix .el-icon-time:before {
  background: red;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange {
  width: 400px;
  height: 32px;
  border-color: #A7ADB4;
  border-radius: 2px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange:focus {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange.is-active {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange.is-active .el-range__icon {
  background-color: #0854A1;
  color: white;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range__icon {
  height: 30px;
  margin-left: -10px;
  padding: 0 2px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range-editor.is-active,
.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range-editor.is-active:hover {
  border: 1px solid #0854A1;
  background-color: #EBF5FE;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-input__icon {
  line-height: 100%;
  color: #0854A1;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-input__prefix {
  width: 30px;
  height: 30px;
  left: 1px;
  background: #fff;
  top: 1px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-input__prefix .el-icon-time:before {
  width: 14px;
  height: 16px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-input__suffix {
  width: 25px;
  right: 0px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange:hover {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range-separator {
  line-height: 24px;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-date-table td.end-date span,
.scenatorStyle.el-date-editor.el-date-editor--daterange .el-date-table td.start-date span {
  background-color: #0854A1;
  color: white;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range-input::-webkit-input-placeholder {
  color: #6A6D70;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange::-ms-input-placeholder {
  color: #6A6D70;
}

.scenatorStyle.el-date-editor.el-date-editor--daterange .el-range__close-icon {
  width: 15px;
  margin-left: 20px;
}

.scenatorStyle.el-picker-panel table {
  display: table;
}

.scenatorStyle.el-picker-panel th,
.scenatorStyle.el-picker-panel td {
  border: 0px;
}

.scenatorStyle.el-picker-panel tr:nth-child(2n) {
  background-color: transparent;
}

.scenatorStyle.el-picker-panel tr {
  border-top: 0px;
}

.scenatorStyle.el-picker-panel .el-date-table td.end-date span,
.scenatorStyle.el-picker-panel .el-date-table td.start-date span {
  background-color: #0854A1;
}

.scenatorStyle.el-picker-panel .el-range-editor.is-active,
.scenatorStyle.el-picker-panel .el-range-editor.is-active:hover {
  border-color: #0854A1;
}

.scenatorStyle.el-picker-panel .el-date-table td.today span {
  color: #0854A1;
}

.scenatorStyle.el-picker-panel .el-date-table td.current:not(.disabled) span {
  background-color: #0854A1;
  color: #fff;
}

.scenatorStyle.el-picker-panel .el-date-table td.available:hover {
  color: #0854A1;
}

.scenatorStyle.el-picker-panel .el-picker-panel__icon-btn:hover {
  color: #0854A1;
}

.scenatorStyle.el-picker-panel td.end-date span,
.scenatorStyle.el-picker-panel td.start-date span {
  background-color: #0854A1;
}

.scenator_ztree {
  padding: 0px;
  border: 1px solid red;
  overflow-x: hidden;
  height: 150px;
}

.scenator_ztree .ztree ul,
.scenator_ztree .ztree li {
  list-style: none;
}

.scenator_ztree .ztree li a {
  width: 100%;
  padding: 0px;
  display: inline-block;
}

.scenator_ztree .ztree span {
  display: inline-block;
}

.scenator_ztree .ztree .node_name {
  font-size: 14px;
  color: #182A4E;
  line-height: 28px;
}

.scenator_ztree .ztree li {
  background: rgba(0, 0, 0, 0);
  white-space: nowrap;
}

.scenator_ztree .ztree li ul {
  padding: 0;
}

.scenator_ztree .ztree li span.button.switch {
  height: 28px;
  width: 16px;
  padding-right: 4px;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease-in-out;
}

.scenator_ztree .ztree li span.button.switch.bottom_close,
.scenator_ztree .ztree li span.button.switch.roots_close,
.scenator_ztree .ztree li span.button.switch.center_close,
.scenator_ztree .ztree li span.button.switch.root_close,
.scenator_ztree .ztree li span.button.switch.bottom_open,
.scenator_ztree .ztree li span.button.switch.roots_open,
.scenator_ztree .ztree li span.button.switch.center_open,
.scenator_ztree .ztree li span.button.switch.root_open,
.scenator_ztree .ztree li span.button.switch.noline_open,
.scenator_ztree .ztree li span.button.switch.noline_close {
  background: none;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid rgba(0, 0, 0, 0);
}

.scenator_ztree .ztree li span.button.switch.bottom_close::after,
.scenator_ztree .ztree li span.button.switch.roots_close::after,
.scenator_ztree .ztree li span.button.switch.center_close::after,
.scenator_ztree .ztree li span.button.switch.root_close::after,
.scenator_ztree .ztree li span.button.switch.noline_close::after {
  content: " ";
  width: 16px;
  height: 16px;
  border: 1px solid rgba(0, 0, 0, 0);
  display: inline-block;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  background-size: 100% 100%;
  transform: rotate(-90deg);
}

.scenator_ztree .ztree li span.button.switch.bottom_open::after,
.scenator_ztree .ztree li span.button.switch.roots_open::after,
.scenator_ztree .ztree li span.button.switch.center_open::after,
.scenator_ztree .ztree li span.button.switch.root_open::after,
.scenator_ztree .ztree li span.button.switch.noline_open::after {
  content: " ";
  width: 16px;
  height: 16px;
  border: 1px solid rgba(0, 0, 0, 0);
  display: inline-block;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  background-size: 100% 100%;
  transform: rotate(0deg);
}

.scenator_ztree .ztree span.button.switch.curSelectedNode ~ span {
  background: #EBF5FE;
}

.scenator_ztree .ztree li span.button.chk {
  height: 28px;
  width: 23px;
  margin: 0;
  padding-right: 6px;
  border-radius: 2px;
  background: none;
  cursor: pointer;
  position: relative;
  z-index: 2;
  border: 1px solid rgba(0, 0, 0, 0);
}

.scenator_ztree .ztree li span.button.chk::after {
  content: " ";
  width: 16px;
  height: 16px;
  border: 1px solid #A7ADB4;
  display: inline-block;
  border-radius: 2px;
}

.scenator_ztree .ztree li span.button.chk.checkbox_true_full::after {
  background: none;
  border-radius: 2px;
  border: 1px solid #A7ADB4;
}

.scenator_ztree .ztree li span.button.chk.checkbox_true_full_focus::after {
  background: none;
  border-radius: 2px;
  border: 1px solid #A7ADB4;
}

.scenator_ztree .ztree li span.button.chk.checkbox_false_full_focus::after {
  background: url("./scenator_style/scenator_rightIncon.svg") no-repeat center;
  background-size: 100% 100%;
  border-color: #0854A1;
}

.scenator_ztree .ztree li span.button.chk.checkbox_false_full::after {
  background: url("./scenator_style/scenator_rightIncon.svg") no-repeat center;
  background-size: 100% 100%;
  border-radio: 2px;
}

.scenator_ztree .ztree li a {
  height: 28px;
}

.scenator_ztree .ztree li a.curSelectedNode {
  background-color: #EBF5FE;
  border: 0px;
  opacity: 1;
  height: 27px;
  border-left: 0px;
}

.scenator_ztree .ztree li a.curSelectedNode .node_name {
  color: #0854A1;
}

.scenator_ztree .ztree li a:hover {
  text-decoration: none;
}

.scenator_ztree .ztree li span.button.ico_open,
.scenator_ztree .ztree li span.button.ico_docu,
.scenator_ztree .ztree li span.button.ico_close {
  background: none;
  width: 16px;
  height: 28px;
  vertical-align: middle;
  position: relative;
  z-index: 2;
  margin-right: 5px;
}

.scenator_ztree .ztree li span.button.ico_close::after {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_tree_folderClose.svg") no-repeat center;
  border: 1px solid rgba(0, 0, 0, 0);
  position: absolute;
  left: 0px;
  top: 3px;
}

.scenator_ztree .ztree li span.button.ico_open::after {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_tree_folderOpen.svg") no-repeat center;
  border: 1px solid rgba(0, 0, 0, 0);
  position: absolute;
  left: 0px;
  top: 3px;
}

.scenator_ztree .ztree li span.button.ico_docu::after {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_tree_file.svg") no-repeat center;
  border: 1px solid rgba(0, 0, 0, 0);
  position: absolute;
  left: 0px;
  top: 3px;
}

.scenator_ztree .ztree .curSelectedNode::before {
  left: -100%;
}

.scenator_ztree .ztree .level1[treenode] span[treenode_switch] {
  padding-left: 24px;
  background: none;
}

.scenator_ztree .ztree .level2[treenode] span[treenode_switch] {
  padding-left: 48px;
  background: none;
}

.scenator_ztree .ztree .level3[treenode] span[treenode_switch] {
  padding-left: 72px;
  background: none;
}

.scenator_ztree .ztree .level4[treenode] span[treenode_switch] {
  padding-left: 96px;
  background: none;
}

.scenator_ztree .ztree .level5[treenode] span[treenode_switch] {
  padding-left: 120px;
  background: none;
}

.scenator_ztree .ztree .level6[treenode] span[treenode_switch] {
  padding-left: 144px;
  background: none;
}

.scenator_ztree .ztree .level7[treenode] span[treenode_switch] {
  padding-left: 168px;
  background: none;
}

.scenator_ztree .ztree .level8[treenode] span[treenode_switch] {
  padding-left: 192px;
  background: none;
}

.scenator_ztree .ztree .level9[treenode] span[treenode_switch] {
  padding-left: 216px;
  background: none;
}

.scenator_ztree .ztree .level10[treenode] span[treenode_switch] {
  padding-left: 240px;
  background: none;
}

.scenator_ztree .ztree .level11[treenode] span[treenode_switch] {
  padding-left: 264px;
  background: none;
}

.scenator_ztree .ztree .level12[treenode] span[treenode_switch] {
  padding-left: 288px;
  background: none;
}

.scenator_ztree .ztree .level13[treenode] span[treenode_switch] {
  padding-left: 312px;
  background: none;
}

.scenator_ztree .ztree .level14[treenode] span[treenode_switch] {
  padding-left: 336px;
  background: none;
}

.scenator_ztree .ztree .level15[treenode] span[treenode_switch] {
  padding-left: 360px;
  background: none;
}

.scenator_ztree .ztree .level16[treenode] span[treenode_switch] {
  padding-left: 384px;
  background: none;
}

.scenator_ztree .ztree .level17[treenode] span[treenode_switch] {
  padding-left: 408px;
  background: none;
}

.scenator_ztree .ztree .level18[treenode] span[treenode_switch] {
  padding-left: 432px;
  background: none;
}

.scenator_ztree .ztree .level19[treenode] span[treenode_switch] {
  padding-left: 456px;
  background: none;
}

.scenator_ztree .ztree .level20[treenode] span[treenode_switch] {
  padding-left: 480px;
  background: none;
}

.scenator_ztree .ztree .level21[treenode] span[treenode_switch] {
  padding-left: 504px;
  background: none;
}

.scenator_ztree .ztree .level22[treenode] span[treenode_switch] {
  padding-left: 528px;
  background: none;
}

.scenator_ztree .ztree .level23[treenode] span[treenode_switch] {
  padding-left: 552px;
  background: none;
}

.scenator_ztree .ztree .level24[treenode] span[treenode_switch] {
  padding-left: 576px;
  background: none;
}

.scenator_ztree .ztree .level25[treenode] span[treenode_switch] {
  padding-left: 600px;
  background: none;
}

.scenator_ztree .ztree .level26[treenode] span[treenode_switch] {
  padding-left: 624px;
  background: none;
}

.scenator_ztree .ztree .level27[treenode] span[treenode_switch] {
  padding-left: 648px;
  background: none;
}

.scenator_ztree .ztree .level28[treenode] span[treenode_switch] {
  padding-left: 672px;
  background: none;
}

.scenator_ztree .ztree .level29[treenode] span[treenode_switch] {
  padding-left: 696px;
  background: none;
}

.scenator_ztree .ztree .level30[treenode] span[treenode_switch] {
  padding-left: 720px;
  background: none;
}

.scenator_ztree .ztree .curSelectedNode {
  position: relative;
  z-index: 0;
}

.scenator_ztree .ztree .curSelectedNode::before {
  content: " ";
  background-color: #EBF5FE;
  margin-top: 0px;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
}

.scenator_ztree[scenatorstyle=icon] .ztree li span.button.chk {
  display: none;
}

.scenator_ztree[scenatorstyle=checkBox] .ztree li span.button.ico_open,
.scenator_ztree[scenatorstyle=checkBox] .ztree li span.button.ico_close,
.scenator_ztree[scenatorstyle=checkBox] .ztree li span.button.ico_docu {
  display: none;
}

.scenator_ztree[scenatorstyle=main] .ztree li span.button.chk {
  display: none;
}

.scenator_ztree[scenatorstyle=main] .ztree li span.button.ico_open,
.scenator_ztree[scenatorstyle=main] .ztree li span.button.ico_close,
.scenator_ztree[scenatorstyle=main] .ztree li span.button.ico_docu {
  display: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level0[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level0[treenode] > a {
  width: calc(100% - 26px);
}

.scenator_ztree[scenatorstyle=checkBox] .level0[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level0[treenode] .curSelectedNode::before {
  left: -45px;
}

.scenator_ztree[scenatorstyle=checkBox] .level1[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level1[treenode] span[treenode_switch] {
  padding-left: 24px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level1[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level1[treenode] > a {
  width: calc(100% - 50px);
}

.scenator_ztree[scenatorstyle=checkBox] .level1[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level1[treenode] .curSelectedNode::before {
  left: -67px;
}

.scenator_ztree[scenatorstyle=checkBox] .level2[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level2[treenode] span[treenode_switch] {
  padding-left: 48px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level2[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level2[treenode] > a {
  width: calc(100% - 74px);
}

.scenator_ztree[scenatorstyle=checkBox] .level2[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level2[treenode] .curSelectedNode::before {
  left: -91px;
}

.scenator_ztree[scenatorstyle=checkBox] .level3[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level3[treenode] span[treenode_switch] {
  padding-left: 72px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level3[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level3[treenode] > a {
  width: calc(100% - 98px);
}

.scenator_ztree[scenatorstyle=checkBox] .level3[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level3[treenode] .curSelectedNode::before {
  left: -115px;
}

.scenator_ztree[scenatorstyle=checkBox] .level4[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level4[treenode] span[treenode_switch] {
  padding-left: 96px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level4[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level4[treenode] > a {
  width: calc(100% - 122px);
}

.scenator_ztree[scenatorstyle=checkBox] .level4[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level4[treenode] .curSelectedNode::before {
  left: -139px;
}

.scenator_ztree[scenatorstyle=checkBox] .level5[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level5[treenode] span[treenode_switch] {
  padding-left: 120px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level5[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level5[treenode] > a {
  width: calc(100% - 146px);
}

.scenator_ztree[scenatorstyle=checkBox] .level5[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level5[treenode] .curSelectedNode::before {
  left: -163px;
}

.scenator_ztree[scenatorstyle=checkBox] .level6[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level6[treenode] span[treenode_switch] {
  padding-left: 144px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level6[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level6[treenode] > a {
  width: calc(100% - 170px);
}

.scenator_ztree[scenatorstyle=checkBox] .level6[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level6[treenode] .curSelectedNode::before {
  left: -187px;
}

.scenator_ztree[scenatorstyle=checkBox] .level7[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level7[treenode] span[treenode_switch] {
  padding-left: 168px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level7[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level7[treenode] > a {
  width: calc(100% - 194px);
}

.scenator_ztree[scenatorstyle=checkBox] .level7[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level7[treenode] .curSelectedNode::before {
  left: -211px;
}

.scenator_ztree[scenatorstyle=checkBox] .level8[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level8[treenode] span[treenode_switch] {
  padding-left: 192px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level8[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level8[treenode] > a {
  width: calc(100% - 218px);
}

.scenator_ztree[scenatorstyle=checkBox] .level8[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level8[treenode] .curSelectedNode::before {
  left: -235px;
}

.scenator_ztree[scenatorstyle=checkBox] .level9[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level9[treenode] span[treenode_switch] {
  padding-left: 216px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level9[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level9[treenode] > a {
  width: calc(100% - 242px);
}

.scenator_ztree[scenatorstyle=checkBox] .level9[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level9[treenode] .curSelectedNode::before {
  left: -259px;
}

.scenator_ztree[scenatorstyle=checkBox] .level10[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level10[treenode] span[treenode_switch] {
  padding-left: 240px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level10[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level10[treenode] > a {
  width: calc(100% - 266px);
}

.scenator_ztree[scenatorstyle=checkBox] .level10[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level10[treenode] .curSelectedNode::before {
  left: -283px;
}

.scenator_ztree[scenatorstyle=checkBox] .level11[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level11[treenode] span[treenode_switch] {
  padding-left: 264px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level11[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level11[treenode] > a {
  width: calc(100% - 290px);
}

.scenator_ztree[scenatorstyle=checkBox] .level11[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level11[treenode] .curSelectedNode::before {
  left: -307px;
}

.scenator_ztree[scenatorstyle=checkBox] .level12[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level12[treenode] span[treenode_switch] {
  padding-left: 288px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level12[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level12[treenode] > a {
  width: calc(100% - 314px);
}

.scenator_ztree[scenatorstyle=checkBox] .level12[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level12[treenode] .curSelectedNode::before {
  left: -331px;
}

.scenator_ztree[scenatorstyle=checkBox] .level13[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level13[treenode] span[treenode_switch] {
  padding-left: 312px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level13[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level13[treenode] > a {
  width: calc(100% - 338px);
}

.scenator_ztree[scenatorstyle=checkBox] .level13[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level13[treenode] .curSelectedNode::before {
  left: -355px;
}

.scenator_ztree[scenatorstyle=checkBox] .level14[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level14[treenode] span[treenode_switch] {
  padding-left: 336px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level14[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level14[treenode] > a {
  width: calc(100% - 362px);
}

.scenator_ztree[scenatorstyle=checkBox] .level14[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level14[treenode] .curSelectedNode::before {
  left: -379px;
}

.scenator_ztree[scenatorstyle=checkBox] .level15[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level15[treenode] span[treenode_switch] {
  padding-left: 360px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level15[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level15[treenode] > a {
  width: calc(100% - 386px);
}

.scenator_ztree[scenatorstyle=checkBox] .level15[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level15[treenode] .curSelectedNode::before {
  left: -403px;
}

.scenator_ztree[scenatorstyle=checkBox] .level16[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level16[treenode] span[treenode_switch] {
  padding-left: 384px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level16[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level16[treenode] > a {
  width: calc(100% - 410px);
}

.scenator_ztree[scenatorstyle=checkBox] .level16[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level16[treenode] .curSelectedNode::before {
  left: -427px;
}

.scenator_ztree[scenatorstyle=checkBox] .level17[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level17[treenode] span[treenode_switch] {
  padding-left: 408px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level17[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level17[treenode] > a {
  width: calc(100% - 434px);
}

.scenator_ztree[scenatorstyle=checkBox] .level17[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level17[treenode] .curSelectedNode::before {
  left: -451px;
}

.scenator_ztree[scenatorstyle=checkBox] .level18[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level18[treenode] span[treenode_switch] {
  padding-left: 432px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level18[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level18[treenode] > a {
  width: calc(100% - 458px);
}

.scenator_ztree[scenatorstyle=checkBox] .level18[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level18[treenode] .curSelectedNode::before {
  left: -475px;
}

.scenator_ztree[scenatorstyle=checkBox] .level19[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level19[treenode] span[treenode_switch] {
  padding-left: 456px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level19[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level19[treenode] > a {
  width: calc(100% - 482px);
}

.scenator_ztree[scenatorstyle=checkBox] .level19[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level19[treenode] .curSelectedNode::before {
  left: -499px;
}

.scenator_ztree[scenatorstyle=checkBox] .level20[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level20[treenode] span[treenode_switch] {
  padding-left: 480px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level20[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level20[treenode] > a {
  width: calc(100% - 506px);
}

.scenator_ztree[scenatorstyle=checkBox] .level20[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level20[treenode] .curSelectedNode::before {
  left: -523px;
}

.scenator_ztree[scenatorstyle=checkBox] .level21[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level21[treenode] span[treenode_switch] {
  padding-left: 504px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level21[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level21[treenode] > a {
  width: calc(100% - 530px);
}

.scenator_ztree[scenatorstyle=checkBox] .level21[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level21[treenode] .curSelectedNode::before {
  left: -547px;
}

.scenator_ztree[scenatorstyle=checkBox] .level22[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level22[treenode] span[treenode_switch] {
  padding-left: 528px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level22[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level22[treenode] > a {
  width: calc(100% - 554px);
}

.scenator_ztree[scenatorstyle=checkBox] .level22[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level22[treenode] .curSelectedNode::before {
  left: -571px;
}

.scenator_ztree[scenatorstyle=checkBox] .level23[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level23[treenode] span[treenode_switch] {
  padding-left: 552px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level23[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level23[treenode] > a {
  width: calc(100% - 578px);
}

.scenator_ztree[scenatorstyle=checkBox] .level23[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level23[treenode] .curSelectedNode::before {
  left: -595px;
}

.scenator_ztree[scenatorstyle=checkBox] .level24[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level24[treenode] span[treenode_switch] {
  padding-left: 576px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level24[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level24[treenode] > a {
  width: calc(100% - 602px);
}

.scenator_ztree[scenatorstyle=checkBox] .level24[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level24[treenode] .curSelectedNode::before {
  left: -619px;
}

.scenator_ztree[scenatorstyle=checkBox] .level25[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level25[treenode] span[treenode_switch] {
  padding-left: 600px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level25[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level25[treenode] > a {
  width: calc(100% - 626px);
}

.scenator_ztree[scenatorstyle=checkBox] .level25[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level25[treenode] .curSelectedNode::before {
  left: -643px;
}

.scenator_ztree[scenatorstyle=checkBox] .level26[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level26[treenode] span[treenode_switch] {
  padding-left: 624px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level26[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level26[treenode] > a {
  width: calc(100% - 650px);
}

.scenator_ztree[scenatorstyle=checkBox] .level26[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level26[treenode] .curSelectedNode::before {
  left: -667px;
}

.scenator_ztree[scenatorstyle=checkBox] .level27[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level27[treenode] span[treenode_switch] {
  padding-left: 648px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level27[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level27[treenode] > a {
  width: calc(100% - 674px);
}

.scenator_ztree[scenatorstyle=checkBox] .level27[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level27[treenode] .curSelectedNode::before {
  left: -691px;
}

.scenator_ztree[scenatorstyle=checkBox] .level28[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level28[treenode] span[treenode_switch] {
  padding-left: 672px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level28[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level28[treenode] > a {
  width: calc(100% - 698px);
}

.scenator_ztree[scenatorstyle=checkBox] .level28[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level28[treenode] .curSelectedNode::before {
  left: -715px;
}

.scenator_ztree[scenatorstyle=checkBox] .level29[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level29[treenode] span[treenode_switch] {
  padding-left: 696px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level29[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level29[treenode] > a {
  width: calc(100% - 722px);
}

.scenator_ztree[scenatorstyle=checkBox] .level29[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level29[treenode] .curSelectedNode::before {
  left: -739px;
}

.scenator_ztree[scenatorstyle=checkBox] .level30[treenode] span[treenode_switch],
.scenator_ztree[scenatorstyle="check+Icon"] .level30[treenode] span[treenode_switch] {
  padding-left: 720px;
  background: none;
}

.scenator_ztree[scenatorstyle=checkBox] .level30[treenode] > a,
.scenator_ztree[scenatorstyle="check+Icon"] .level30[treenode] > a {
  width: calc(100% - 746px);
}

.scenator_ztree[scenatorstyle=checkBox] .level30[treenode] .curSelectedNode::before,
.scenator_ztree[scenatorstyle="check+Icon"] .level30[treenode] .curSelectedNode::before {
  left: -763px;
}

.scenatorStyle.el-tree .el-tree-node__content {
  height: 28px;
}

.scenatorStyle.el-tree .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #EBF5FE;
}

.scenatorStyle.el-tree .el-icon-caret-right:before {
  content: " ";
  width: 16px;
  width: 16px;
  position: absolute;
  background-size: 100% 100%;
}

.scenatorStyle.el-tree .el-tree-node__content > .el-tree-node__expand-icon {
  margin: 6px;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  padding: 0px;
  width: 16px;
  height: 16px;
}

.scenatorStyle.el-tree .el-tree-node__expand-icon.expanded {
  transform: rotate(-90deg);
}

.scenatorStyle.el-tree .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
  background: none;
}

.scenatorStyle.el-tree .el-tree-node__label {
  color: #182A4E;
  position: relative;
}

.scenatorStyle.el-tree .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content .el-tree-node__label {
  font-size: 14px;
  color: #0854A1;
}

.scenatorStyle.el-tree .el-checkbox__inner {
  border: 1px solid #A7ADB4;
  width: 16px;
  height: 16px;
}

.scenatorStyle.el-tree .el-checkbox__inner::after {
  left: 5px;
  top: 2px;
}

.scenatorStyle.el-tree .el-checkbox__inner:hover {
  border: 1px solid #0854A1;
}

.scenatorStyle.el-tree .el-checkbox__input.is-checked .el-checkbox__inner,
.scenatorStyle.el-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
}

.scenatorStyle.el-tree .el-checkbox__inner::after {
  border-color: #0854A1;
}

.scenatorStyle.el-tree[scenatorStyle=icon] .el-tree-node__label {
  text-indent: 20px;
}

.scenatorStyle.el-tree[scenatorStyle=icon] .el-tree-node__expand-icon.expanded ~ .el-tree-node__label:before {
  background: url("./scenator_style/scenator_tree_folderOpen.svg") no-repeat center;
}

.scenatorStyle.el-tree[scenatorStyle=icon] .el-tree-node__label:before {
  content: " ";
  width: 16px;
  height: 16px;
  position: absolute;
  background: url("./scenator_style/scenator_tree_folderClose.svg") no-repeat center;
  left: 0px;
  top: 2px;
  background-size: 100% 100%;
}

.scenatorStyle.el-tree[scenatorStyle=icon] .el-tree-node__expand-icon.is-leaf ~ .el-tree-node__label:before {
  background: url("./scenator_style/scenator_tree_file.svg") no-repeat center;
}

.scenatorStyle table {
  margin: 0px;
}

.scenatorStyle th,
.scenatorStyle td {
  border: 0px;
  border-bottom: 1px solid rgba(24, 42, 78, 0.1);
}

.scenatorStyle .el-table .cell,
.scenatorStyle .el-table--border .el-table__cell:first-child .cell {
  padding-left: 0;
}

.scenatorStyle .el-table th.el-table__cell > .cell {
  padding-right: 0px;
  color: #182A4E;
  font-weight: 500;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] {
  border: 1px solid rgba(24, 42, 78, 0.1);
  border-bottom: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table--border::after,
.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table--group::after,
.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table::before {
  background-color: transparent;
  width: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table--border::after,
.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table--group::after,
.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table::before {
  content: none;
}

.scenatorStyle .el-table .el-table__header th,
.scenatorStyle .el-table .el-table__body td {
  padding: 8px 12px;
}

.scenatorStyle .el-table th.el-table__cell {
  background: #EDEDED;
}

.scenatorStyle .el-table td.el-table__cell,
.scenatorStyle .el-table th.el-table__cell.is-leaf {
  border-color: rgba(24, 42, 78, 0.1);
}

.scenatorStyle .el-table--border::after,
.scenatorStyle .el-table--group::after,
.scenatorStyle .el-table::before {
  background-color: rgba(24, 42, 78, 0.1);
}

.scenatorStyle .el-table--border,
.scenatorStyle .el-table--group {
  border-color: rgba(24, 42, 78, 0.1);
}

.scenatorStyle .el-table td.el-table__cell div {
  color: #182A4E;
}

.scenatorStyle .el-table__row:hover td {
  background-color: #EBF5FE;
}

.scenatorStyle .el-table__body tr.current-row > td.el-table__cell {
  border-bottom-color: #0854A1;
}

.scenatorStyle .el-table[scenatorStyle=scenator_smallTable] .el-table__row td,
.scenatorStyle .el-table[scenatorStyle=scenator_smallTable] .el-table__header th {
  padding: 4px 12px;
}

.scenatorStyle .el-table__cell.el-table__expanded-cell {
  padding: 0px;
}

.scenatorStyle .el-table .el-button--text {
  color: #0854A1;
  line-height: 20px;
  padding: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-icon-arrow-right:before {
  content: " ";
  width: 16px;
  height: 16px;
  position: absolute;
  top: -3px;
  left: -3px;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  transform: rotate(-90deg);
  background-size: 100% 100%;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expand-icon--expanded .el-icon-arrow-right:before {
  transform: rotate(270deg);
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expand-icon > .el-icon {
  left: 6px;
  right: 4px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell {
  padding: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table {
  border-left: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table tbody tr:nth-last-child(1) .el-table__cell {
  border-bottom: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table tbody tr td:nth-last-child(1) {
  border-right: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table tbody tr td:nth-of-type(1) {
  border-left: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table thead tr th:nth-last-child(1) {
  border-right: 0px;
}

.scenatorStyle .el-table[scenatorstyle=scenator_expandNoBorder] .el-table__expanded-cell .el-table thead tr td:nth-first-child(1) {
  border-left: 0px;
}

.scenatorStyle.el-pagination .el-pagination__sizes .el-input .el-input__inner:hover {
  border-color: #0854A1;
}

.scenatorStyle.el-pagination .el-pagination__sizes .el-select:hover .el-input__suffix {
  background: #EBF5FE;
}

.scenatorStyle.el-pagination .el-pagination__sizes .el-select:hover .el-input__icon {
  color: #0854A1;
}

.scenatorStyle.el-pagination .el-select .el-input.is-focus .el-input__inner {
  border-color: #0854A1;
}

.scenatorStyle.el-pagination .el-select .el-input.is-focus {
  background: #EBF5FE;
}

.scenatorStyle.el-pagination .el-select .el-input .el-input__inner {
  border-radius: 2px;
}

.scenatorStyle.el-pagination .el-input__suffix-inner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.scenatorStyle.el-pagination .el-select .el-input__icon {
  line-height: inherit;
  width: 28px;
}

.scenatorStyle.el-pagination .el-select {
  height: 100%;
}

.scenatorStyle.el-pagination .el-input {
  height: 100%;
}

.scenatorStyle.el-pagination .el-input__inner {
  height: 100%;
  border-color: #A7ADB4;
  border-radius: 2px;
}

.scenatorStyle.el-pagination .el-select .el-input {
  width: 84px;
}

.scenatorStyle.el-pagination .el-select .el-input__icon {
  width: 20px;
  margin-top: -2px;
  line-height: 32px;
}

.scenatorStyle.el-pagination .el-select .el-input .el-input__inner {
  padding-right: 20px;
}

.scenatorStyle.el-pagination .el-select .el-input__inner {
  padding: 0 5px;
}

.scenatorStyle.el-pagination .el-select .el-input.is-focus .el-input__icon {
  margin-top: -1px;
}

.scenatorStyle.el-pagination .btn-prev {
  position: absolute;
  right: 67px;
  padding: 0px;
}

.scenatorStyle.el-pagination .btn-next {
  position: absolute;
  padding: 0px;
  right: 0;
}

.scenatorStyle.el-pagination button {
  min-width: 20px;
}

.scenatorStyle.el-pagination .el-icon-arrow-right:before {
  top: 5px;
}

.scenatorStyle.el-pagination .el-icon-arrow-left:before {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  transform: rotate(90deg);
  background-size: 100% 100%;
  position: absolute;
  top: 5px;
  left: 2px;
}

.scenatorStyle.el-pagination .el-icon-arrow-right:before {
  content: " ";
  width: 16px;
  height: 16px;
  background: url("./scenator_style/scenator_treeOpen.svg") no-repeat center;
  transform: rotate(-90deg);
  background-size: 100% 100%;
  position: absolute;
  top: 5px;
  left: 2px;
}

.scenatorStyle.el-pagination .btn-prev[disabled=disabled] .el-icon-arrow-left:before {
  opacity: 0.4;
}

.scenatorStyle.el-pagination .btn-next[disabled=disabled] .el-icon-arrow-right:before {
  opacity: 0.4;
}

.scenatorStyle.el-pagination .el-pagination__sizes {
  position: absolute;
  right: 75px;
}

.scenatorStyle.el-pagination .el-pagination__rightwrapper {
  position: relative;
}

.scenatorStyle.el-pagination .el-pagination__total {
  color: #5D697A;
}

.scenatorStyle.el-pagination .scenator_pageNum {
  margin-right: 115px;
  color: #5D697A;
  font-weight: 500;
}

.scenator_selectPopper .el-select-dropdown__item.selected {
  color: #0854A1;
}

.scenator_selectPopper .el-select-dropdown__item.hover,
.scenator_selectPopper .el-select-dropdown__item:hover {
  background-color: #EFF2F4;
}

.scenatorStyle.el-tabs--card .el-tabs__header .el-tabs__nav-wrap::after {
  background: none;
}

.scenatorStyle.el-tabs--card .el-tabs__header .el-tabs__active-bar {
  background-color: #0854A1;
}

.scenatorStyle.el-tabs--card .el-tabs__header .el-tabs__item.is-active {
  color: #0854A1;
}

.scenatorStyle.el-tabs--card .el-tabs__item {
  width: 100px;
  max-width: 240px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-bottom: 1px solid transparent;
  border-left: 1px solid transparent;
  position: relative;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid transparent;
  height: 42px;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom: 2px solid #0854A1;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable:hover {
  padding-left: 20px;
  padding-right: 20px;
}

.scenatorStyle.el-tabs--card .el-tabs__item:hover {
  color: #0854A1;
}

.scenatorStyle.el-tabs--card .el-table__expand-icon {
  height: 20px;
  width: 20px;
}

.scenatorStyle.el-tabs--card .el-table__expand-icon > .el-icon {
  height: 20px;
  width: 20px;
  left: 0px;
  top: 0px;
  margin-left: 0px;
  margin-top: 0px;
}

.scenatorStyle.el-tabs--card .el-table__row.expanded td.el-table__cell {
  border: rgba(0, 0, 0, 0);
}

.scenatorStyle.el-tabs--card .el-tabs__nav-next,
.scenatorStyle.el-tabs--card .el-tabs__nav-prev {
  width: 32px;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  box-shadow: -1px 0px 4px 0px rgba(0, 0, 0, 0.2);
}

.scenatorStyle.el-tabs--card .el-icon-arrow-left,
.scenatorStyle.el-tabs--card .el-icon-arrow-right {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid transparent;
}

.scenatorStyle.el-tabs--card .el-icon-arrow-left:before,
.scenatorStyle.el-tabs--card .el-icon-arrow-right:before {
  content: " ";
}

.scenatorStyle.el-tabs--card .el-icon-arrow-right:hover,
.scenatorStyle.el-tabs--card .el-icon-arrow-left:hover {
  background-color: #EBF5FE;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-tabs--card .el-icon-arrow-right:active,
.scenatorStyle.el-tabs--card .el-icon-arrow-left:active {
  background-color: #0854A1;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-tabs--card .el-icon-plus {
  width: 20px;
  height: 20px;
  margin-left: -6px;
  border: 1px solid transparent;
  transform: scale(1);
}

.scenatorStyle.el-tabs--card .el-icon-plus::before {
  content: " ";
}

.scenatorStyle.el-tabs--card .el-icon-plus:hover {
  background-color: #EBF5FE;
  border-radius: 2px;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-tabs--card .el-icon-plus:active {
  background-color: #114171;
  border-radius: 2px;
  border: 1px solid #0854A1;
}

.scenatorStyle.el-tabs--card .el-tabs__new-tab {
  width: 20px;
  height: 41px;
  margin: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0px;
  padding-left: 10px;
  border-radius: 0px;
  border-left: #D9D9D9 1px solid;
}

.scenatorStyle.el-tabs--card .el-tabs__nav-wrap.is-scrollable {
  padding: 0 34px 0 32px;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close {
  padding: 0px;
  height: 14px;
  width: 14px;
  border: 1px solid rgba(0, 0, 0, 0);
  box-sizing: border-box;
  border-radius: 2px;
  position: absolute;
  right: 2%;
  top: 13px;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:hover {
  border-color: #0854A1;
  background-color: #EBF5FE;
}

.scenatorStyle.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .el-icon-close:active {
  border-color: #0854A1;
  background-color: #114171;
}

.scenatorStyle.el-tabs--card .el-tabs__item .el-icon-close {
  display: none;
}

.scenatorStyle.el-tabs--card .el-tabs__item:hover .el-icon-close {
  display: block;
}

.scenatorStyle.el-tabs--card .el-icon-close:hover {
  border-radius: 2px;
}

.scenatorStyle.el-tabs--card .el-icon-close:before {
  content: " ";
}

.scenatorStyle.el-tabs--card[scenatorStyle=scenator_tabs_auto] .el-tabs__item {
  width: 100px;
  max-width: 240px;
}

.scenatorStyle.el-tabs--card[scenatorStyle=scenator_tabs_auto] .el-tabs__nav-scroll {
  background: #EFEFEF;
}

.scenatorStyle.el-tabs--card[scenatorStyle=scenator_tabs_auto] > .el-tabs__header .el-tabs__item.is-active {
  background: #fff;
}

.scenatorTitle {
  box-sizing: content-box;
  padding: 4px 8px;
  color: white;
  background: rgba(0, 0, 0, 0.35);
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 3;
  display: none;
  border-radius: 2px;
  word-break: keep-all;
}