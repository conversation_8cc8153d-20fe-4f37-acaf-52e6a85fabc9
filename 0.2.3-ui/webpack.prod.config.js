/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:53:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-24 17:13:50
 */
const path = require("path");
const webpack = require("webpack");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const { VueLoaderPlugin } = require("vue-loader");
const htmlWebpackplugin = require("html-webpack-plugin");
const resolve = (arg) => {
	return path.resolve(__dirname, arg);
};

module.exports = {
	mode: "none",
	entry: { app: ["babel-polyfill", "./src/main.js"] },
	output: {
		path: path.resolve(__dirname, "dist"),
		filename: "js/app[hash].js",
	},
	devtool: "source-map",
	resolve: {
		extensions: [".js", ".vue", ".json"],
		// 设置别名
		alias: {
			"@": resolve("src"), // 这样配置后 @ 可以指向 src 目录
		},
	},
	plugins: [
		new CleanWebpackPlugin(),
		new webpack.DefinePlugin({
			"process.env": '"production"',
		}),
		new MiniCssExtractPlugin({
			filename: "app.[hash].css",
			chunkFilename: "app.[hash].css",
		}),
		new htmlWebpackplugin({
			//创建一个在内存中生成的html页面的插件
			favicon: "./public/favicon.ico",
			template: path.join(__dirname, "./public/index.html"),
			filename: "index.html",
		}),
		new webpack.LoaderOptionsPlugin({
			// test: /\.xxx$/, // may apply this only for some modules
			options: {
				// html模板
				index: path.resolve(__dirname, "../dist/index.html"),
				// Paths
				assetsRoot: path.resolve(__dirname, "../dist"),
				assetsSubDirectory: "/",
				assetsPublicPath: "/",
				// 生产环境的souce map
				productionSourceMap: true,
				// 开启静态文件的Gzip压缩
				productionGzip: false,
				productionGzipExtensions: ["js", "css"],
			},
		}),
		new VueLoaderPlugin(),
	],
	// 模块，loader
	module: {
		rules: [
			{
				test: /\.vue$/,
				loader: "vue-loader",
				exclude: /node_modules/,
			},
			{
				test: /\.js$/,
				loader: "babel-loader",
				exclude: /node_modules/,
			},
			{
				test: /\.less$/,
				use: ["style-loader", "css-loader", "less-loader"],
			},
			{
				test: /\.css$/,
				use: [MiniCssExtractPlugin.loader, "css-loader"],
			},
			{
				test: /.(png|jpe?g|gif|svg|woff|ttf|woff2?)(\?.*)?$/,
				loader: "url-loader",
				options: {
					limit: 10000,
					name: "./assets/[name].[hash:7].[ext]",
					esModule: false,
				},
			},
		],
	},
};
