/*
 * @Descriptin:
 * @Version: 0.1
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:53:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-02 14:13:47
 */
const { resolve } = require("path");
const BundleAnalyzerPlugin =
	require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

const production = process.env.NODE_ENV === "production";

// plugins配置
var plugins = [];

if (production) {
	plugins.push(new BundleAnalyzerPlugin());
}

module.exports = {
	transpileDependencies: ["jian-pinyin"],
	lintOnSave: false,

	// 输出打包的文件夹名称
	outputDir: "ControlLogs",

	// 输出打包静态资源目录

	assetsDir: "./",

	publicPath: "./",

	configureWebpack: {
		plugins: plugins,
		// 路径别名
		resolve: {
			alias: {
				"@": resolve("src"),
			},
		},
	},
	devServer: {
		host: "localhost",
		port: 8080,
		proxy: {
			"/AIMS": {
				target: "http://*************:10010",
				ws: true,
				changeOrigin: true,
			},
			"/UAMS": {
				target: "http://************:8400/",
				ws: true,
				changeOrigin: true,
				pathRewrite: {
					// 转发
					"^/UAMS": "",
				},
			},
		},
	},
};
