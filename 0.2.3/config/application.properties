server.servlet.context-path=/ClickLog
server.port=8084

##########                   è¦æ±                   ############

# ç®åmatching.datasource.typeæ¯æoracleä¸mysqlä¸¤ç§æ°æ®åº
# spring.profiles.activeå¡«åä¸matching.datasource.typeä¸è´
# urlæ ¼å¼ï¼
#   MySQLï¼jdbc:mysql://<ip>:<port>/<æ°æ®åºåç§°>
#   oracleï¼jdbc:oracle:thin:@//<ip>:<port>/<å®ä¾åç§°>

###########                   ç¤ºä¾                   ############
#spring.profiles.active=oracle
#clicklog.datasource.type=oracle
#clicklog.datasource.url=***************************************
#clicklog.datasource.username=click
#clicklog.datasource.password=root

#spring.profiles.active=mysql
#clicklog.datasource.type=mysql
#clicklog.datasource.url=****************************************************************************************************************************************************************************************************************************
#clicklog.datasource.username=root
#clicklog.datasource.password=new-password
#################################################################
spring.profiles.active=oracle
clicklog.datasource.type=oracle
clicklog.datasource.url=***************************************
clicklog.datasource.username=click
clicklog.datasource.password=root

# éç½®uamsçurlå°åï¼ç«¯å£è¯·éç½®ä¸ºUAMSèä¸æ¯Gatewayç«¯å£
uams.url=http://localhost:8440/UAMS

# éç½®scenatorçurlå°åï¼ç«¯å£éç½®ä¸ºGatewayç«¯å£
scenator.url=http://localhost:8099/Scenator


redis.host=127.0.0.1
redis.port=8379
redis.password=admin
redis.database=14
# Rediså­å¨çè®¿é®çæ°æ®ç»è®¡è¿ææ¶é´ï¼åä½ï¼ç§ï¼0ä¸ºä¸ä»Redisåå¼ç´æ¥éæ°è®¡ç®è®¿é®ç
redis.expireTime=30