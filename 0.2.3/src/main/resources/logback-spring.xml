<?xml version="1.0" encoding="UTF-8"?>

<!--
	【log4j2日志级别】
	debug:调试,一般作为最低级别进行使。
	info:输出重要的信息,使用较多。
	warn:警告,有些信息不是错误,但是也要进行提示。
	error:错误信息，使用较多。
	fatal：致命错误。
	========================================
	【输出方式】
	Console：输出到控制台
	File：输出到文件
	=========================================
	【布局方式】
	SimpleLayout：以简单的形式显示
	HTMLLayout：以HTML表格显示
	PatternLayout：自定义形式显示
	在Log4J2中基本采用PatternLayout自定义日志布局。
	%t：线程名称
	%p：日志级别
	%c：日志消息所在类名
	%m：消息内容
	%M：输出执行方法
	%d：发生时间，%d{yyyy-MM-dd HH:mm:ss,SSS}，输出类似：2011-10-18 22:10:28,921
	%x: 输出和当前线程相关联的NDC(嵌套诊断环境),尤其用到像java servlets这样的多客户多线程的应用中。
	%L：代码中的行数
	%n：换行
	=========================================
	
 -->
<!--
	Configuration
	scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
    scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<!-- 设置上下文名称 -->
	<contextName>project</contextName>

	<timestamp key="bySecond" datePattern="yyyyMMdd'T'HHmmss"/> 

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_HOME" value="./logs" />
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- 按照每天生成日志文件 -->
    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
        <!--日志文件最大的大小-->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>10MB</MaxFileSize>
        </triggeringPolicy>
    </appender>
    <!-- logger 用来设置某一个包或者具体的某一个类的日志打印级别 -->
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder"  level="DEBUG" />
    <logger name="org.hibernate.type.descriptor.sql.BasicExtractor"  level="DEBUG" />
    <logger name="org.hibernate.SQL" level="DEBUG" />
    <logger name="org.hibernate.engine.QueryParameters" level="DEBUG" />
    <logger name="org.hibernate.engine.query.HQLQueryPlan" level="DEBUG" />

    <!--myibatis log configure-->
    <logger name="com.apache.ibatis" level="DEBUG"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <logger name="java.sql.PreparedStatement" level="DEBUG"/>

    <logger name="org.springframework.jdbc.core.JdbcTemplate" additivity="false" level="DEBUG"/>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>
</configuration>