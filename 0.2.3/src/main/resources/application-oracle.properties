spring.datasource.url=${clicklog.datasource.url}
spring.datasource.username=${clicklog.datasource.username}
spring.datasource.password=${clicklog.datasource.password}

logging.level.org.hibernate.SQL=info
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n
# spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
# spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=15
spring.jpa.properties.hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringSessionContext
spring.jpa.properties.hibernate.jdbc.batch_size=500
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates =true
spring.jpa.properties.hibernate.connection.autocommit=true
spring.jpa.properties.hibernate.show-sql=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration/${clicklog.datasource.type}

jasypt.encryptor.password=rzon2022