package com.rzontech.clicklog.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class Config {

    public static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static String uamsUrl;

    private static String scenatorUrl;

    private static String redisHost;

    private static int redisPort;

    private static String redisPassword;

    private static int redisDatabase;

    private static int redisExpireTime;

    private static String datasourceType;

    public static String getUamsUrl(){
        return uamsUrl;
    }

    @Value("${uams.url}")
    private void setUamsUrl(String uamsUrl) {
        Config.uamsUrl = uamsUrl;
    }

    public static String getScenatorUrl(){
        return scenatorUrl;
    }

    @Value("${scenator.url}")
    private void setScenatorUrl(String scenatorUrl){
        Config.scenatorUrl = scenatorUrl;
    }

    public static String getRedisHost() {
        return redisHost;
    }

    @Value("${redis.host}")
    private void setRedisHost(String redisHost) {
        Config.redisHost = redisHost;
    }

    public static int getRedisPort() {
        return redisPort;
    }

    @Value("${redis.port}")
    private void setRedisPort(int redisPort) {
        Config.redisPort = redisPort;
    }

    public static String getRedisPassword() {
        return redisPassword;
    }

    @Value("${redis.password}")
    private void setRedisPassword(String redisPassword) {
        Config.redisPassword = redisPassword;
    }

    public static int getRedisDatabase() {
        return redisDatabase;
    }

    @Value("${redis.database}")
    private void setRedisDatabase(int redisDatabase) {
        Config.redisDatabase = redisDatabase;
    }

    public static int getRedisExpireTime(){
        return redisExpireTime;
    }

    @Value("${redis.expireTime}")
    private void setRedisExpireTime(int redisExpireTime){
        Config.redisExpireTime = redisExpireTime;
    }

    @Value("${clicklog.datasource.type}")
    private void setDatasourceType(String datasourceType) {
        Config.datasourceType = datasourceType;
    }

    @Bean
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(20);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("update-statistics-to-redis");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        return executor;
    }

    public static Timestamp parseDate(String time) throws ParseException {
        if (!StringUtils.isEmpty(time))
            return new Timestamp(new SimpleDateFormat("yyyy-MM-dd").parse(time).getTime());
        return null;
    }

    public static Timestamp parseDateTime(String time) throws ParseException {
        if (!StringUtils.isEmpty(time))
            return new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(time).getTime());
        return null;
    }

    public static String getDatasourceType() {
        return datasourceType.toLowerCase(Locale.ROOT);
    }

    public static String getTimestampSQLFormat(Timestamp timestamp) {
        if (datasourceType.equalsIgnoreCase("oracle")) {
            return getTimestampSQLOracle(timestamp);
        }
        if (datasourceType.equalsIgnoreCase("mysql"))  {
            return getTimestampSQLMySQL(timestamp);
        }
        return getTimestampSQLOracle(timestamp);
    }

    public static String getTimestampSQLOracle(Timestamp timestamp) {
        return "to_timestamp('" + timestamp + "','YYYY-MM-DD HH24:MI:SS.FF')";
    }

    public static String getTimestampSQLMySQL(Timestamp timestamp) {
        return "'" + timestamp.toString() + "'";
    }
}
