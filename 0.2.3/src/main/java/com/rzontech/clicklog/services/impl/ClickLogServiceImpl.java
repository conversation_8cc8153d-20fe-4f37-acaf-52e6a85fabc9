package com.rzontech.clicklog.services.impl;

import com.rzontech.clicklog.adapter.AccountAdapter;
import com.rzontech.clicklog.dao.ClickLogDAO;
import com.rzontech.clicklog.model.ClickLog;
import com.rzontech.clicklog.model.ClickLogSearchModel;
import com.rzontech.clicklog.model.ClickStatistics;
import com.rzontech.clicklog.model.Group;
import com.rzontech.clicklog.services.ClickLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ClickLogServiceImpl implements ClickLogService {

    @Resource
    ClickLogDAO clickLogDAO;

    @Resource
    AccountAdapter accountAdapter;

    @Override
    public void addClickLogBatch(List<ClickLog> clickLogs) {
        clickLogDAO.addBatch(clickLogs);
    }

    @Override
    public List<ClickLog> getClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType) {
        return clickLogDAO.getClickLogs(searchModel, columnName, sortType, true);
    }

    @Override
    public List<ClickStatistics> getClickStatistics(String cookie, String groupId, Timestamp startTime, Timestamp endTime) {
        Map<String, Integer> dataFromUAMS;
        if (StringUtils.isEmpty(groupId)) {
            dataFromUAMS = accountAdapter.getAccountNum(cookie);
        } else
            dataFromUAMS = accountAdapter.getAccountNum(cookie, groupId);
        List<ClickStatistics> dataFromSQL = clickLogDAO.getVisitStatistics(groupId, startTime, endTime);
        return calculateVisitRate(dataFromUAMS, dataFromSQL);
    }

    private List<ClickStatistics> calculateVisitRate(Map<String, Integer> accountNumMap, List<ClickStatistics> statisticsFromSQL) {
        List<ClickStatistics> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : accountNumMap.entrySet())
            result.add(calculateVisitRatePerContent(entry.getKey(), entry.getValue(), statisticsFromSQL));
        return result;
    }

    private ClickStatistics calculateVisitRatePerContent(String logContent, int accountNum, List<ClickStatistics> clickStatistics) {
        DecimalFormat df = new DecimalFormat("#0.00");
        for (ClickStatistics statistics : clickStatistics) {
            if (logContent.equals(statistics.getLogContent())) {
                double per = (double) statistics.getVisitAccountNum() / (double) accountNum;
                String percentage = accountNum == 0 ? String.valueOf(0) : df.format(per * 100) + "%";
                return statistics.accountNum(accountNum).visitPercentage(percentage);
            }
        }
        return new ClickStatistics().logContent(logContent).accountNum(accountNum);
    }

    public List<Group> getGroupsByUser(String userId) {
        return accountAdapter.getGroupsByUser(userId);
    }

    @Override
    public long getLogNum(ClickLogSearchModel searchModel) {
        return clickLogDAO.getLogNum(searchModel);
    }

    @Override
    //todo 暂未添加logBehavior列内容
    public File exportClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType) throws IOException {
        final SXSSFWorkbook wb = new SXSSFWorkbook();
        int sheetLimit = 1024 * 1024 - 1;
        List<ClickLog> clickLogs = getClickLogs(searchModel, columnName, sortType);
        for (int sheetIndex = 0; sheetIndex <= clickLogs.size() / sheetLimit; sheetIndex++) {
            Sheet sheet = wb.createSheet(String.valueOf(sheetIndex));
            final Row titleRow = sheet.createRow(0);
            setTitleRow(titleRow);
            for (int i = 0; i < (sheetIndex + 1) * sheetLimit && i < clickLogs.size(); i++) {
                Row dataRow = sheet.createRow((i + 1) % sheetLimit);
                ClickLog clickLog = clickLogs.get(i);
                dataRow.createCell(0).setCellValue(clickLog.getUserName());
                dataRow.createCell(1).setCellValue(clickLog.getSecGroupName());
                dataRow.createCell(2).setCellValue(clickLog.getTerGroupName());
                dataRow.createCell(3).setCellValue(clickLog.getLogContent());
                dataRow.createCell(4).setCellValue(clickLog.getCreateTime().toString());
                dataRow.createCell(5).setCellValue(clickLog.getIp());
            }
        }
        File outputFile = File.createTempFile("访问日志", ".xlsx");
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            wb.write(fos);
        }
        return outputFile;
    }

    private void setTitleRow(Row titleRow) {
        titleRow.createCell(0).setCellValue("操作人");
        titleRow.createCell(1).setCellValue("二级机构名称");
        titleRow.createCell(2).setCellValue("三级机构名称");
        titleRow.createCell(3).setCellValue("访问内容");
        titleRow.createCell(4).setCellValue("操作时间");
        titleRow.createCell(5).setCellValue("ip地址");
    }

    @Override
    @Async
    public void setHistoryDataGroupColumn(Timestamp startTime, Timestamp endTime, String sortTypeStatus) {
        ClickLogSearchModel searchModel = new ClickLogSearchModel(null, null, null, null, startTime, endTime, 0, -1);
        List<ClickLog> clickLogs = clickLogDAO.getClickLogs(searchModel, "CREATE_TIME", sortTypeStatus, false);
        Map<String, String[]> groupsSortInClass = processGroupClass(accountAdapter.getAllGroups());
        Map<Timestamp, List<ClickLog>> createTimeLogMap = clickLogs.stream().collect(Collectors.groupingBy(ClickLog::getCreateTime, LinkedHashMap::new, Collectors.toList()));
        List<ClickLog> result = new ArrayList<>();
        createTimeLogMap.forEach((key, value) -> {
            String secGroupId = null, secGroupName = null, terGroupId = null, terGroupName = null;
            for (ClickLog log : value) {
                String groundGroupId = log.getGroupId();
                List<Group> allGroup = getAllGroupsThroughParentId(groundGroupId);
                for (Group group : allGroup) {
                    String groupId = group.getId();
                    if (groupsSortInClass.get(groupId) != null && groupsSortInClass.get(groupId)[0].equals("2")) {
                        secGroupId = groupId;
                        secGroupName = groupsSortInClass.get(groupId)[1];
                    }
                    if (groupsSortInClass.get(groupId) != null && groupsSortInClass.get(groupId)[0].equals("3")) {
                        terGroupId = groupId;
                        terGroupName = groupsSortInClass.get(groupId)[1];
                    }
                }
            }
            for (ClickLog log : value) {
                if (!StringUtils.isEmpty(secGroupId) && StringUtils.isEmpty(log.getSecGroupId())) {
                    log.secGroupId(secGroupId);
                    log.secGroupName(secGroupName);
                }
                if (!StringUtils.isEmpty(terGroupId) && StringUtils.isEmpty(log.getTerGroupId())) {
                    log.terGroupId(terGroupId);
                    log.terGroupName(terGroupName);
                }
                result.add(log);
            }
        });
        clickLogDAO.addBatch(result);
        log.info("处理完毕，更新" + result.size() + "条");
    }

    private Map<String, String[]> processGroupClass(List<Group> groups) {
        Map<String, String[]> groupClassMap = new HashMap<>();
        List<Group> firstGroup = new ArrayList<>();
        List<Group> secGroup = new ArrayList<>();
        for (Group group : groups) {
            if (StringUtils.isEmpty(group.getParentId()) && !group.isSysGroup()) {
                groupClassMap.put(group.getId(), new String[]{"1", group.getName()});
                firstGroup.add(group);
            }
        }
        for (Group group : firstGroup) {
            for (Group temp : groups) {
                if (!StringUtils.isEmpty(temp.getParentId()) && temp.getParentId().equals(group.getId())) {
                    groupClassMap.put(temp.getId(), new String[]{"2", temp.getName()});
                    secGroup.add(temp);
                }
            }
        }
        for (Group group : secGroup) {
            for (Group temp : groups) {
                if (!StringUtils.isEmpty(temp.getParentId()) && temp.getParentId().equals(group.getId())) {
                    groupClassMap.put(temp.getId(), new String[]{"3", temp.getName()});
                }
            }
        }
        return groupClassMap;
    }

    @Override
    public Group getFirstClassGroup(List<Group> groups) {
        for (Group group : groups) {
            if (StringUtils.isEmpty(group.getParentId()) && !group.isSysGroup()) {
                return group;
            }
        }
        return null;
    }

    @Override
    public Group getOffspringGroup(List<Group> groups, Group currentGroup) {
        if (currentGroup == null) {
            return null;
        }
        for (Group group : groups) {
            if (!StringUtils.isEmpty(group.getParentId()) && group.getParentId().equals(currentGroup.getId())) {
                return group;
            }
        }
        return null;
    }

    @Override
    public List<Group> getAllGroupsThroughParentId(String groupId) {
        Set<Group> groups = new LinkedHashSet<>();
        getGroupsThroughParentId(groups, groupId);
        return new ArrayList<>(groups);
    }

    private void getGroupsThroughParentId(Set<Group> groups, String groupId) {
        if (!StringUtils.isEmpty(groupId)) {
            Group group = accountAdapter.getGroup(groupId);
            if (group != null) {
                groups.add(group);
                if (!StringUtils.isEmpty(group.getParentId())) {
                    getGroupsThroughParentId(groups, group.getParentId());
                }
            }
        }
    }


}
