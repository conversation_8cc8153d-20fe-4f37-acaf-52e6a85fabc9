package com.rzontech.clicklog.services;

import com.rzontech.clicklog.model.ClickLog;
import com.rzontech.clicklog.model.ClickLogSearchModel;
import com.rzontech.clicklog.model.ClickStatistics;
import com.rzontech.clicklog.model.Group;

import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.List;

public interface ClickLogService {

    void addClickLogBatch(List<ClickLog> clickLogs);

    List<ClickLog> getClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType);

    List<ClickStatistics> getClickStatistics(String cookie, String groupId, Timestamp startTime, Timestamp endTime);

    List<Group> getGroupsByUser(String userId);

    long getLogNum(ClickLogSearchModel searchModel);

    File exportClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType) throws IOException;

    void setHistoryDataGroupColumn(Timestamp startTime, Timestamp endTime, String sortTypeStatus);

    Group getFirstClassGroup(List<Group> groups);

    Group getOffspringGroup(List<Group> groups, Group first);

    List<Group> getAllGroupsThroughParentId(String groupId);
}
