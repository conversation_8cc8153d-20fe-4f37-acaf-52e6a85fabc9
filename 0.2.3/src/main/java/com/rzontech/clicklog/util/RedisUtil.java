package com.rzontech.clicklog.util;

import com.rzontech.clicklog.config.Config;
import redis.clients.jedis.Jedis;

public class RedisUtil {

    public static Jedis getRedisConn(String ip, int port, String password, int database){
        Jedis jedis = new Jedis(ip, port);
        if (password != null && !password.isEmpty())
            jedis.auth(password);
        jedis.select(database);
        return jedis;
    }

    public static Jedis getRedisConn(){
        String ip = Config.getRedisHost();
        int port = Config.getRedisPort();
        String password = Config.getRedisPassword();
        int database = Config.getRedisDatabase();
        return getRedisConn(ip, port, password, database);
    }

}
