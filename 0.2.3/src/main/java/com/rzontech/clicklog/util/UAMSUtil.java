package com.rzontech.clicklog.util;

import cn.hutool.http.HttpUtil;
import com.fulongtech.uams.token.utils.UAMSTokenUtils;
import com.rzontech.clicklog.config.Config;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class UAMSUtil {

    public  String getUamsToken(){
        Map<String,Object> map = new HashMap<>();
        map.put("serverName","SuposApp");
        map.put("credential", UAMSTokenUtils.encrypt("admin"));
        String uams_auth = Config.getUamsUrl() + "/token";
        return HttpUtil.post(uams_auth,map);
    }

    public  Map<String, String> getUamsHeader(){
        Map<String,String> headers = new HashMap<>();
        headers.put("Authorization-UAMS", getUamsToken());
        headers.put("content-type","application/json;charset=utf-8");
        return headers;
    }


}
