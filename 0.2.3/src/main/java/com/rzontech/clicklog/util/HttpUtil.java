package com.rzontech.clicklog.util;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;

public class HttpUtil {

    public static HttpServletResponse getResponse(final HttpServletResponse res, final int code, final String mes) {
        res.setHeader("Content-type", "text/html;charset=UTF-8");
        try {
            res.setStatus(code);
            res.getWriter().write(mes);
        } catch (IOException e) {
            e.printStackTrace();
        }
        res.setCharacterEncoding("UTF-8");
        return res;
    }

    public static String getIp(HttpServletRequest request){
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
    }

    public static String getCookie(HttpServletRequest request){
        Cookie[] strings = request.getCookies();
        String cookie = "";
        for (Cookie string : strings) {
            if ( "FULONGTECH_SESSION".equals(string.getName()) ) {
                cookie = string.getName() + "=" + string.getValue();
                break;
            }
        }
        return cookie;
    }

    public static ResponseEntity<FileSystemResource> setFileResponseEntity(File file) throws UnsupportedEncodingException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", "attachment; fileName="+  file.getName() +";filename*=utf-8''"+ URLEncoder.encode(file.getName(),"UTF-8"));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        headers.add("Last-Modified", new Date().toString());
        headers.add("ETag", String.valueOf(System.currentTimeMillis()));
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentLength(file.length())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new FileSystemResource(file));
    }




}
