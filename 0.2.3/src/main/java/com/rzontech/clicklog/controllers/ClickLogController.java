package com.rzontech.clicklog.controllers;

import com.rzontech.clicklog.config.Config;
import com.rzontech.clicklog.model.*;
import com.rzontech.clicklog.services.ClickLogService;
import com.rzontech.clicklog.util.HttpUtil;
import com.rzontech.clicklog.util.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@RestController
public class ClickLogController {

    private static final Logger logger = LoggerFactory.getLogger(ClickLogController.class);

    @Resource
    ClickLogService clickLogService;

    @RequestMapping(value = "/index")
    public String index() {
        return "./static/index.html";
    }

    @GetMapping(value = "/test")
    public Object test(String userId, HttpServletRequest request) {
        return null;
    }

    /**
     * @api {POST} /addClickLog 添加访问条目
     * @apiDescription 添加访问条目，参数格式为Json
     * @apiName addClickLog
     * @apiGroup ClickLog
     *
     * @apiParam {String} logType 操作类型
     * @apiParam {String} logContent 操作功能
     * @apiParam {String} logBehavior 操作内容
     * @apiParam {String} userId 用户Id
     * @apiParam {String} groupId 用户组Id
     * @apiParam {Object[]} [group] 用户组，选填。如果不填此项，则由后端直接访问UAMS查询此用户的用户组
     * @apiParam {String} group[id] 用户组Id
     * @apiParam {String} group[parentId] 父级用户组Id
     * @apiParam {String} group[name] 用户组名称
     * @apiParam {Boolean} group[sysGroup] 是否为管理用户组
     *
     * @apiParamExample {json} Request-Example:
     * {
     *     "logType": "访问日志",
     *     "logContent": "三维",
     *     "logBehavior": "访问",
     *     "userId": "b129ce91a180425d0436f919d8bb3adf",
     *     "userName": "test",
     *     "groups": [
     *         {
     *             "id": "BEFE2FF1A20F4C419C8A94B7213C5218",
     *             "parentId": null,
     *             "name": "系统管理组",
     *             "sysGroup": true
     *         },
     *         {
     *             "id": "AEFE2FF1A20F4C419C8A94B7213C5218",
     *             "parentId": null,
     *             "name": "管理组",
     *             "sysGroup": true
     *         }
     *     ]
     * }
     *
     * @apiSuccess Success 添加访问条目成功
     * @apiError (500 Internal Server Error) InternalServerError 添加访问条目失败
     */
    @PostMapping(value = "/addClickLog")
    public void addLog(@RequestBody ClickLogDTO clickLogDTO, HttpServletRequest request, HttpServletResponse response) {
        try {
            String ip = HttpUtil.getIp(request);
            Timestamp createTime = new Timestamp(System.currentTimeMillis());
            List<ClickLog> clickLogs = transferToClickLogs(clickLogDTO, ip, createTime);
            clickLogService.addClickLogBatch(clickLogs);
            HttpUtil.getResponse(response, 200, "添加访问条目成功");
        } catch (Exception e) {
            e.printStackTrace();
            HttpUtil.getResponse(response, 500, "添加访问条目失败");
        }
    }

    /**
     * @api {GET} /getClickLogs 获取访问条目
     * @apiDescription 获取访问条目
     * @apiName getClickLogs
     * @apiGroup ClickLog
     *
     * @apiParam {String} [userId] 用户Id
     * @apiParam {String} [groupId] 用户组Id
     * @apiParam {String} [logContent] 操作功能
     * @apiParam {String} [logBehavior] 操作内容
     * @apiParam {String} [startTime] 查询起始时间，格式yyyy-MM-dd，不填入该参数，则代表不限制起始时间
     * @apiParam {String} [endTime] 查询终止时间，格式yyyy-MM-dd，不填入该参数，则代表不限制终止时间
     * @apiParam {int} currentPage 当前页
     * @apiParam {int} pageRecords 每页条目数量
     * @apiParam {int} sortTypeStatus 排序方式，1代表ASC升序，2代表DESC降序
     *
     * @apiSuccessExample {json} Success-Response:
     * HTTP/1.1 200 OK
     * {
     *     "totalSize": 2,
     *     "logList": [
     *         {
     *             "id": "4028818b7d8ea07e017d8ea595120002",
     *             "logType": "访问日志",
     *             "logContent": "三维",
     *             "logBehavior": "",
     *             "userId": "b129ce91a180425d0436f919d8bb3adf",
     *             "userName": "test",
     *             "groups": [
     *                 {
     *                     "id": "0616fb2776137a886049bfdbf896a0bf",
     *                     "parentId": null,
     *                     "name": "11",
     *                     "sysGroup": false
     *                 }
     *             ],
     *             "ip": "127.0.0.1",
     *             "createTime": "2021-12-06"
     *         },
     *         {
     *             "id": "4028818b7d8ea07e017d8eaf7e230005",
     *             "logType": "访问日志",
     *             "logContent": "三维",
     *             "logBehavior": "",
     *             "userId": "b129ce91a180425d0436f919d8bb3adf",
     *             "userName": "test",
     *             "groups": [
     *                 {
     *                     "id": "0616fb2776137a886049bfdbf896a0bf",
     *                     "parentId": null,
     *                     "name": "11",
     *                     "sysGroup": false
     *                 }
     *             ],
     *             "ip": "127.0.0.1",
     *             "createTime": "2021-12-06"
     *         }
     *     ],
     *     "currentPage": 1
     * }
     */
    @GetMapping(value = "/getClickLogs")
    public Map<String, Object> getClickLogs(String userId, String groupId, String logContent, String logBehavior, String startTime, String endTime, int currentPage, int pageRecords, int sortTypeStatus) {
        Instant startAll = Instant.now();
        logger.info("获取用户访问操作");
        Map<String, Object> result = new HashMap<>();
        Timestamp startTimestamp, endTimestamp;
        final int startRow = (currentPage - 1) * pageRecords;
        final int endRow = startRow + pageRecords;
        try {
            startTimestamp = Config.parseDate(startTime);
            endTimestamp = plusOneDay(Config.parseDate(endTime));
            ClickLogSearchModel searchModel = new ClickLogSearchModel(userId, groupId, logContent, logBehavior, startTimestamp, endTimestamp, startRow, endRow);


            Instant start = Instant.now();
            List<ClickLog> clickLogs = clickLogService.getClickLogs(searchModel, "CREATE_TIME", convertToSortType(sortTypeStatus));
            List<ClickLogWithGroups> logList = new ArrayList<>();
            Instant end = Instant.now();
            logger.info("获取访问条目耗时： " + Duration.between(start, end) + "-----");
            //todo 返回Group的方法待定
            start = Instant.now();
            for (ClickLog clickLog : clickLogs) {
                List<Group> groups = clickLogService.getGroupsByUser(clickLog.getUserId());
                logList.add(new ClickLogWithGroups(clickLog, groups));
            }
            end = Instant.now();
            logger.info("获取用户组耗时： " + Duration.between(start, end));
            start = Instant.now();
            result.put("totalSize", clickLogService.getLogNum(searchModel));
            end = Instant.now();
            logger.info("获取条目数量耗时： " + Duration.between(start, end));
            result.put("currentPage", currentPage);
            result.put("logList", logList);
            Instant endAll = Instant.now();
            logger.info("共耗时： " + Duration.between(startAll, endAll));
            return result;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @api {GET} /getClickStatistics 获取访问率数据
     * @apiDescription 获取访问率数据
     * @apiName getClickStatistics
     * @apiGroup ClickLog
     *
     * @apiParam {String} [groupId] 用户组Id
     * @apiParam {String} [startTime] 查询起始时间，格式yyyy-MM-dd，不填入该参数，则代表不限制起始时间
     * @apiParam {String} [endTime] 查询终止时间，格式yyyy-MM-dd，不填入该参数，则代表不限制终止时间
     *
     * @apiSuccessExample {json} Success-Response:
     *[
     *     {
     *         "logContent": "文档",
     *         "accountNum": 3,
     *         "visitAccountNum": 1,
     *         "visitTimes": 5,
     *         "visitPercentage": "33.33%"
     *     },
     *     {
     *         "logContent": "三维",
     *         "accountNum": 2,
     *         "visitAccountNum": 2,
     *         "visitTimes": 21,
     *         "visitPercentage":"100.00%"
     *     }
     * ]
     */
    @GetMapping(value = "/getClickStatistics")
    public List<ClickStatistics> getClickStatistics(String groupId, String startTime, String endTime, HttpServletRequest request) {
        String cookie = HttpUtil.getCookie(request);
        Timestamp startTimestamp, endTimestamp;
        try {
            startTimestamp = Config.parseDate(startTime);
            endTimestamp = plusOneDay(Config.parseDate(endTime));
            return clickLogService.getClickStatistics(cookie, groupId, startTimestamp, endTimestamp);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping(value = "/deleteRedisKeyImplicit")
    public long deleteRedisKeyImplicit(String key) {
        try (Jedis jedis = RedisUtil.getRedisConn()) {
            return jedis.del(key);
        }
    }

    /**
     * @api {GET} /exportClickLogs 导出访问条目文件
     * @apiDescription 导出访问条目文件
     * @apiName exportClickLogs
     * @apiGroup ClickLog
     *
     * @apiParam {String} [userId] 用户Id
     * @apiParam {String} [groupId] 用户组Id
     * @apiParam {String} [logContent] 操作功能
     * @apiParam {String} [logBehavior] 操作内容
     * @apiParam {String} [startTime] 查询起始时间，格式yyyy-MM-dd，不填入该参数，则代表不限制起始时间
     * @apiParam {String} [endTime] 查询终止时间，格式yyyy-MM-dd，不填入该参数，则代表不限制终止时间
     * @apiParam {int} currentPage 当前页
     * @apiParam {int} pageRecords 每页条目数量
     * @apiParam {int} sortTypeStatus 排序方式，1代表ASC升序，2代表DESC降序
     *
     * @apiSuccess {ResponseEntity} - 导出文件
     * @apiError (500 Internal Server Error) InternalServerError 导出失败
     */
    @GetMapping(value = "/exportClickLogs")
    public ResponseEntity<FileSystemResource> exportClickLogs(String userId, String groupId, String logContent, String logBehavior, String startTime, String endTime, int sortTypeStatus, final HttpServletResponse response) {
        try {
            Timestamp startTimestamp, endTimestamp;
            startTimestamp = Config.parseDate(startTime);
            endTimestamp = plusOneDay(Config.parseDate(endTime));
            ClickLogSearchModel searchModel = new ClickLogSearchModel(userId, groupId, logContent, logBehavior, startTimestamp, endTimestamp, 0, -1);
            File file = clickLogService.exportClickLogs(searchModel, "CREATE_TIME", convertToSortType(sortTypeStatus));
            return HttpUtil.setFileResponseEntity(file);
        } catch (IOException | ParseException exception) {
            exception.printStackTrace();
            HttpUtil.getResponse(response, 500, "导出失败");
            return null;
        }
    }

    /**
     * @api {GET} /processHistoryData 处理1.0版本生成的访问数据
     * @apiDescription 因为1.0版本中没有保留用户组之间的关系，在导出访问记录时，会无法判断二级与三级机构。此接口将根据原有数据查询UAMS，判断二三级机构，并存入相应的字段。此接口只能通过url访问方式执行。
     * @apiName processHistoryData
     * @apiGroup ClickLog
     *
     * @apiParam {String} [startTime] 查询起始时间，格式yyyy-MM-dd，不填入该参数，则代表不限制起始时间
     * @apiParam {String} [endTime] 查询终止时间，格式yyyy-MM-dd，不填入该参数，则代表不限制终止时间
     */
    @GetMapping(value = "/processHistoryData")
    public String setHistoryDataGroupColumn(String startTime, String endTime) throws ParseException {
        logger.info("处理历史数据");
        clickLogService.setHistoryDataGroupColumn(Config.parseDateTime(startTime), Config.parseDateTime(endTime), "DESC");
        return "异步处理历史数据，结果显示在后台控制台中";
    }

    private String convertToSortType(int sortTypeStatus) {
        if (sortTypeStatus == 1)
            return "ASC";
        else if (sortTypeStatus == 2)
            return "DESC";
        else
            return null;
    }

    private Timestamp plusOneDay(Timestamp timestamp) {
        if (timestamp == null)
            return null;
        LocalDateTime localDateTime = timestamp.toLocalDateTime();
        localDateTime = localDateTime.plusDays(1);
        return Timestamp.valueOf(localDateTime);
    }

    private List<ClickLog> transferToClickLogs(ClickLogDTO rawData, String ip, Timestamp createTime){
        List<ClickLog> clickLogs = new ArrayList<>();
        List<Group> groups = rawData.getGroups();
        if (groups == null || groups.isEmpty()) {
            groups = clickLogService.getGroupsByUser(rawData.getUserId());
        }
        List<Group> allGroupsUsersBelong = new ArrayList<>(groups);
        for (Group group : groups) {
            allGroupsUsersBelong.addAll(clickLogService.getAllGroupsThroughParentId(group.getParentId()));
        }
        Group firstGroup = clickLogService.getFirstClassGroup(allGroupsUsersBelong);
        Group secGroup = clickLogService.getOffspringGroup(allGroupsUsersBelong, firstGroup);
        Group terGroup = clickLogService.getOffspringGroup(allGroupsUsersBelong, secGroup);
        for (Group group: allGroupsUsersBelong) {
            ClickLog temp = new ClickLog();
            temp.logType(rawData.getLogType())
                    .logContent(rawData.getLogContent())
                    .logBehavior(rawData.getLogBehavior())
                    .userId(rawData.getUserId())
                    .userName(rawData.getUserName())
                    .groupId(group.getId())
                    .groupName(group.getName())
                    .ip(ip)
                    .createTime(createTime);
            if (secGroup != null) {
                temp.secGroupId(secGroup.getId()).secGroupName(secGroup.getName());
            }
            if (terGroup != null) {
                temp.terGroupId(terGroup.getId()).terGroupName(terGroup.getName());
            }
            clickLogs.add(temp);
        }
        return clickLogs;
    }

}
