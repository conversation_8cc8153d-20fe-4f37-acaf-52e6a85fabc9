package com.rzontech.clicklog.dao.impl;

import com.rzontech.clicklog.adapter.AccountAdapter;
import com.rzontech.clicklog.config.Config;
import com.rzontech.clicklog.dao.ClickLogDAO;
import com.rzontech.clicklog.model.ClickLog;
import com.rzontech.clicklog.model.ClickLogSearchModel;
import com.rzontech.clicklog.model.ClickStatistics;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ClickLogDAOImpl extends BaseDAOImpl<ClickLog, String> implements ClickLogDAO {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private AccountAdapter accountAdapter;

    @Override
    public long getLogNum(ClickLogSearchModel searchModel) {
        StringBuffer sql = prepareNumSql(searchModel.getUserId(), searchModel.getGroupId(), searchModel.getLogContent(), searchModel.getLogBehavior(),
                searchModel.getStartTime(), searchModel.getEndTime());
        Query query = entityManager.createNativeQuery(sql.toString());
        Number result = (Number) query.getResultList().get(0);
        return result.longValue();
    }

    @SuppressWarnings("unchecked")
    public List<ClickLog> getClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType, boolean isGroupBy) {
        StringBuffer sql = new StringBuffer();
        if (Config.getDatasourceType().equals("oracle")) {
            sql = prepareSearchSqlOracle(searchModel.getUserId(), searchModel.getGroupId(), searchModel.getLogContent(), searchModel.getLogBehavior(),
                    searchModel.getStartTime(), searchModel.getEndTime(), searchModel.getStartRow(), searchModel.getEndRow(), columnName, sortType, isGroupBy);
        }
        else if (Config.getDatasourceType().equals("mysql")) {
            sql = prepareSearchSqlMySQL(searchModel.getUserId(), searchModel.getGroupId(), searchModel.getLogContent(), searchModel.getLogBehavior(),
                    searchModel.getStartTime(), searchModel.getEndTime(), searchModel.getStartRow(), searchModel.getEndRow(), columnName, sortType, isGroupBy);
        }
        Query query = entityManager.createNativeQuery(sql.toString(), ClickLog.class);
        return (List<ClickLog>) query.getResultList();
    }

    private StringBuffer prepareSearchSqlOracle(String userId, String groupId, String logContent, String logBehavior, Timestamp startTime, Timestamp endTime, int startRow, int endRow, String columnName, String sortType, boolean isGroupBy) {
        StringBuffer sql = new StringBuffer("SELECT * FROM (");
        if (isGroupBy) {
            if (StringUtils.isEmpty(groupId)) {
                sql.append("select ROWNUM NO, T.* from ( SELECT * FROM CLICK_LOGS T where id in (select max(id) from CLICK_LOGS group by create_Time)");
            } else {
                List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
                sql.append("select ROWNUM NO, T.* from ( SELECT * FROM CLICK_LOGS where id in (select max(id) from (select * from CLICK_LOGS where GROUP_ID IN (").append(listToString(groupIds)).append(")) group by create_Time)");
            }
        }
        else {
            if (StringUtils.isEmpty(groupId)) {
                sql.append("select ROWNUM NO, T.* from ( SELECT * FROM CLICK_LOGS WHERE 1=1");
            } else {
                List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
                sql.append("select ROWNUM NO, T.* from ( SELECT * FROM CLICK_LOGS where id in (select id from CLICK_LOGS where GROUP_ID IN (").append(listToString(groupIds)).append("))");
            }
        }
        if (!StringUtils.isEmpty(userId))
            sql.append(" AND USER_ID = '").append(userId).append("'");
        if (!StringUtils.isEmpty(logContent))
            sql.append(" AND LOG_CONTENT LIKE '%").append(logContent).append("%'");
        if (!StringUtils.isEmpty(logBehavior))
            sql.append(" AND LOG_BEHAVIOR LIKE '%").append(logBehavior).append("%'");
        if (startTime != null)
            sql.append(" AND CREATE_TIME >= ").append(Config.getTimestampSQLOracle(startTime));
        if (endTime != null)
            sql.append(" AND CREATE_TIME < ").append(Config.getTimestampSQLOracle(endTime));
        sql.append(" order by ").append(columnName).append(" ").append(sortType);
        sql.append(" ) T");
        if (endRow != -1) {
            sql.append(" WHERE ROWNUM <= ").append(endRow);
            sql.append(") where NO > ").append(startRow);
        } else {
            sql.append(")");
        }

        return sql;
    }

    private StringBuffer prepareSearchSqlMySQL(String userId, String groupId, String logContent, String logBehavior, Timestamp startTime, Timestamp endTime, int startRow, int endRow, String columnName, String sortType, boolean isGroupBy) {
        StringBuffer sql = new StringBuffer();
        if (isGroupBy) {
            if (StringUtils.isEmpty(groupId)) {
                sql.append("SELECT * FROM CLICK_LOGS T where id in (select max(id) from CLICK_LOGS group by create_Time)");
            } else {
                List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
                sql.append("SELECT * FROM CLICK_LOGS where id in (select max(id) from (select * from CLICK_LOGS where GROUP_ID IN (").append(listToString(groupIds)).append(")) TEMP group by create_Time)");
            }
        }
        else {
            if (StringUtils.isEmpty(groupId)) {
                sql.append("SELECT * FROM CLICK_LOGS WHERE 1=1");
            } else {
                List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
                sql.append("SELECT * FROM CLICK_LOGS where id in (select id from CLICK_LOGS where GROUP_ID IN (").append(listToString(groupIds)).append("))");
            }
        }
        if (!StringUtils.isEmpty(userId))
            sql.append(" AND USER_ID = '").append(userId).append("'");
        if (!StringUtils.isEmpty(logContent))
            sql.append(" AND LOG_CONTENT LIKE '%").append(logContent).append("%'");
        if (!StringUtils.isEmpty(logBehavior))
            sql.append(" AND LOG_BEHAVIOR LIKE '%").append(logBehavior).append("%'");
        if (startTime != null)
            sql.append(" AND CREATE_TIME >= ").append(Config.getTimestampSQLMySQL(startTime));
        if (endTime != null)
            sql.append(" AND CREATE_TIME < ").append(Config.getTimestampSQLMySQL(endTime));
        sql.append(" order by ").append(columnName).append(" ").append(sortType);
        if (endRow != -1) {
            sql.append(" LIMIT ").append(startRow).append(", ").append(endRow-startRow);
        }
        return sql;
    }

    private StringBuffer prepareNumSql(String userId, String groupId, String logContent, String logBehavior, Timestamp startTime, Timestamp endTime) {
        StringBuffer sql = new StringBuffer();
        if (StringUtils.isEmpty(groupId)) {
            sql.append("select count(*) from CLICK_LOGS T where id in (select max(id) from CLICK_LOGS group by create_Time)");
        } else {
            List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
            sql.append("select count(*) from CLICK_LOGS T where id in (select max(id) from (select * from CLICK_LOGS where GROUP_ID IN (").append(listToString(groupIds)).append(")) TEMP group by create_Time)");
        }
        if (!StringUtils.isEmpty(userId))
            sql.append(" AND USER_ID = '").append(userId).append("'");
        if (!StringUtils.isEmpty(logContent))
            sql.append(" AND LOG_CONTENT LIKE '%").append(logContent).append("%'");
        if (!StringUtils.isEmpty(logBehavior))
            sql.append(" AND LOG_BEHAVIOR LIKE '%").append(logBehavior).append("%'");
        if (startTime != null)
            sql.append(" AND CREATE_TIME >= ").append(Config.getTimestampSQLFormat(startTime));
        if (endTime != null)
            sql.append(" AND CREATE_TIME < ").append(Config.getTimestampSQLFormat(endTime));
        return sql;
    }

    @SuppressWarnings("unchecked")
    public List<ClickStatistics> getVisitStatistics(String groupId, Timestamp startTime, Timestamp endTime) {
        String sql = createSql(groupId, startTime, endTime);
        Query query = entityManager.createNativeQuery(sql);
        List<Object[]> resultList = query.getResultList();
        List<ClickStatistics> result = new ArrayList<>();
        for (Object[] objects : resultList) {
            result.add(new ClickStatistics().logContent((String) objects[0])
                    .visitAccountNum(((Number) objects[1]).intValue())
                    .visitTimes(((Number) objects[2]).intValue()));
        }
        return result;
    }

    private String createSql(String groupId, Timestamp startTime, Timestamp endTime) {
        StringBuilder sql = new StringBuilder("SELECT LOG_CONTENT, COUNT(DISTINCT USER_ID), COUNT(DISTINCT CREATE_TIME)  FROM CLICK_LOGS WHERE 1 = 1");
        if (!StringUtils.isEmpty(groupId)) {
            List<String> groupIds = accountAdapter.addGroupOffspringIdIfExists(groupId);
            String groupIdsStr = listToString(groupIds);
            sql.append(" AND GROUP_ID IN (").append(groupIdsStr).append(")");
        }
        if (startTime != null)
            sql.append(" AND CREATE_TIME >= ").append(Config.getTimestampSQLFormat(startTime));
        if (endTime != null)
            sql.append(" AND CREATE_TIME < ").append(Config.getTimestampSQLFormat(endTime));
        sql.append(" GROUP BY LOG_CONTENT");
        return sql.toString();
    }

    private String listToString(List<String> groupIds) {
        StringBuffer sb = new StringBuffer();
        groupIds.forEach(groupId -> sb.append("'").append(groupId).append("'").append(","));
        return sb.substring(0, sb.lastIndexOf(","));
    }

    /*private static String getMySQLOrderByCh(String sortColumn, String sortType) {
        return "CONVERT( " + sortColumn + " USING gbk ) " + sortType;
    }

    private static String getOracleOrderByCh(String sortColumn, String sortType) {
        return "NLSSORT(" + sortColumn + ",'NLS_SORT = SCHINESE_PINYIN_M') " + sortType;
    }*/


}
