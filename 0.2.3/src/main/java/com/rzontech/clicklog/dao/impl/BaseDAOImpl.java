package com.rzontech.clicklog.dao.impl;

import com.rzontech.clicklog.dao.BaseDAO;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.Serializable;
import java.util.List;

public class BaseDAOImpl<T, I extends Serializable> implements BaseDAO<T, I> {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void add(T model) {
        entityManager.persist(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<T> models) {
        int size =  models.size();
        for (int i = 0; i < size; i++) {
            T model = models.get(i);
            entityManager.merge(model);
            if (i % 1000 == 0 || i==(size-1)) { // 每1000条数据执行一次，或者最后不足1000条时执行
                entityManager.flush();
                entityManager.clear();
            }
        }
    }

    @Override
    public void add(String hql, Object... args) {
        Query query = entityManager.createQuery(hql);
        int i = 0;
        for(Object arg:args) {
            System.out.println(arg);
            query.setParameter(++i,arg);
        }
        query.executeUpdate();
    }

    @Override
    public List<T> findAll(String hql) {
        return entityManager.createQuery(hql).getResultList();
    }
}
