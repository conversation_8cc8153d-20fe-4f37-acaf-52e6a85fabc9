package com.rzontech.clicklog.dao;

import com.rzontech.clicklog.model.ClickLog;
import com.rzontech.clicklog.model.ClickLogSearchModel;
import com.rzontech.clicklog.model.ClickStatistics;

import java.sql.Timestamp;
import java.util.List;

public interface ClickLogDAO extends BaseDAO<ClickLog, String>{

    long getLogNum(ClickLogSearchModel searchModel);

    List<ClickLog> getClickLogs(ClickLogSearchModel searchModel, String columnName, String sortType, boolean isGroupBy);

    List<ClickStatistics> getVisitStatistics(String groupId, Timestamp startTime, Timestamp endTime);


}
