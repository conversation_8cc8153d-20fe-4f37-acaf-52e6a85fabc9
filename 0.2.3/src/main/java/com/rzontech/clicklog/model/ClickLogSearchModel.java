package com.rzontech.clicklog.model;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ClickLogSearchModel {

    private String userId;
    private String groupId;
    private String logContent;
    private String logBehavior;
    private Timestamp startTime;
    private Timestamp endTime;
    private int startRow;
    private int endRow;

    public ClickLogSearchModel(String userId, String groupId, String logContent, String logBehavior, Timestamp startTime, Timestamp endTime, int startRow, int endRow) {
        this.userId = userId;
        this.groupId = groupId;
        this.logContent = logContent;
        this.logBehavior = logBehavior;
        this.startTime = startTime;
        this.endTime = endTime;
        this.startRow = startRow;
        this.endRow = endRow;
    }
}
