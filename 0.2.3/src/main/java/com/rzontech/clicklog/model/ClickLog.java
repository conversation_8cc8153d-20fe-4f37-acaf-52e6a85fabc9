package com.rzontech.clicklog.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.sql.Timestamp;

@Setter
@Getter
@ToString
@Entity
@Table(name = "CLICK_LOGS")
public class ClickLog
{

    @Id
    @GeneratedValue(generator = "paymentableGenerator")
    @GenericGenerator(name = "paymentableGenerator", strategy = "uuid")
    @Column(name = "ID")
    private String id;

    @Column(name = "LOG_TYPE")
    private String logType;

    @Column(name = "LOG_CONTENT")
    private String logContent;

    @Column(name = "lOG_BEHAVIOR")
    private String logBehavior;

    @Column(name = "USER_ID")
    private String  userId;

    @Column(name = "USER_NAME")
    private String  userName;

    @Column(name = "GROUP_ID")
    private String groupId;

    @Column(name = "GROUP_NAME")
    private String groupName;

    @Column(name = "IP")
    private String ip;

    @Column(name = "SEC_GROUP_ID")
    private String secGroupId;

    @Column(name = "SEC_GROUP_NAME")
    private String secGroupName;

    @Column(name = "TER_GROUP_ID")
    private String terGroupId;

    @Column(name = "TER_GROUP_NAME")
    private String terGroupName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "CREATE_TIME")
    private Timestamp createTime;

    public ClickLog id(String id){
        this.setId(id);
        return this;
    }

    public ClickLog logType(String logType){
        this.setLogType(logType);
        return this;
    }

    public ClickLog logBehavior(String logBehavior) {
        this.setLogBehavior(logBehavior);
        return this;
    }

    public ClickLog logContent(String logContent){
        this.setLogContent(logContent);
        return this;
    }

    public ClickLog userId(String userId){
        this.setUserId(userId);
        return this;
    }

    public ClickLog userName(String userName){
        this.setUserName(userName);
        return this;
    }

    public ClickLog groupId(String groupId){
        this.setGroupId(groupId);
        return this;
    }

    public ClickLog groupName(String groupName){
        this.setGroupName(groupName);
        return this;
    }

    public ClickLog ip(String ip){
        this.setIp(ip);
        return this;
    }

    public ClickLog createTime(Timestamp createTime){
        this.setCreateTime(createTime);
        return this;
    }

    public ClickLog secGroupId(String secGroupId){
        this.setSecGroupId(secGroupId);
        return this;
    }

    public ClickLog secGroupName(String groupName){
        this.setSecGroupName(groupName);
        return this;
    }

    public ClickLog terGroupId(String terGroupId){
        this.setTerGroupId(terGroupId);
        return this;
    }

    public ClickLog terGroupName(String terGroupName){
        this.setTerGroupName(terGroupName);
        return this;
    }

}


