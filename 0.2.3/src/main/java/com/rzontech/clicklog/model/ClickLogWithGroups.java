package com.rzontech.clicklog.model;

import lombok.Data;

import javax.persistence.Column;
import java.text.SimpleDateFormat;
import java.util.List;

@Data
public class ClickLogWithGroups {

    private String id;
    private String logType;
    private String logContent;
    private String logBehavior;
    private String  userId;
    private String  userName;
    private List<Group> groups;
    private String ip;
    private String createTime;
    private String secGroupId;
    private String secGroupName;
    private String terGroupId;
    private String terGroupName;

    public ClickLogWithGroups(ClickLog clickLog, List<Group> groups){
        this.id = clickLog.getId();
        this.logType = clickLog.getLogType();
        this.logContent = clickLog.getLogContent();
        this.logBehavior = clickLog.getLogBehavior();
        this.userId = clickLog.getUserId();
        this.userName = clickLog.getUserName();
        this.groups = groups;
        this.ip = clickLog.getIp();
        this.createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(clickLog.getCreateTime());
        this.secGroupId = clickLog.getSecGroupId();
        this.secGroupName = clickLog.getSecGroupName();
        this.terGroupId = clickLog.getTerGroupId();
        this.terGroupName = clickLog.getTerGroupName();
    }

}