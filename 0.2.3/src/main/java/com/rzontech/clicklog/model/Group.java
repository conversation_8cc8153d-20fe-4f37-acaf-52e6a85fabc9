package com.rzontech.clicklog.model;

import lombok.Data;

import java.util.Objects;


@Data
public class Group{
    String id;
    String parentId;
    String name;
    boolean sysGroup;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Group group = (Group) o;
        return Objects.equals(id, group.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}