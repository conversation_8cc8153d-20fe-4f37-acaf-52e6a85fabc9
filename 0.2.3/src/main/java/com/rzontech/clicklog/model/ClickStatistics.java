package com.rzontech.clicklog.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class ClickStatistics implements Serializable {

    private String logContent;
    private int accountNum;
    private int visitAccountNum;
    private int visitTimes;
    private String visitPercentage = "0";

    public ClickStatistics logContent(String logContent) {
        this.logContent = logContent;
        return this;
    }

    public ClickStatistics accountNum(int accountNum){
        this.accountNum = accountNum;
        return this;
    }

    public ClickStatistics visitAccountNum(int visitAccountNum){
        this.visitAccountNum = visitAccountNum;
        return this;
    }

    public ClickStatistics visitTimes(int visitTimes){
        this.visitTimes = visitTimes;
        return this;
    }

    public ClickStatistics visitPercentage(String visitPercentage){
        this.visitPercentage = visitPercentage;
        return this;
    }
}
