package com.rzontech.clicklog.adapter.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rzontech.clicklog.adapter.AsyncProxy;
import com.rzontech.clicklog.config.Config;
import com.rzontech.clicklog.model.Group;
import com.rzontech.clicklog.util.RedisUtil;
import com.rzontech.clicklog.util.UAMSUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Component
public class AsyncProxyImpl implements AsyncProxy {

    private static final Logger logger = LoggerFactory.getLogger(AsyncProxy.class);


    /**
     * 根据用户组ID获取用户，因为存在用户从属三级机构时，同样从属于其父级机构的情况。
     * 为了不重复计算，需要加入到usersId的Set中去重
     */
    @Override
    @Async
    public void updateRedisUsersId(List<Group> groups) {
        try (Jedis jedis = RedisUtil.getRedisConn()) {
            Instant start = Instant.now();
            logger.info("更新访问率缓存");
            for (Group group : groups) {
                String groupId = group.getId();
                jedis.del(groupId);
                HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/groups/" + groupId + "/users");
                httpRequest.addHeaders(new UAMSUtil().getUamsHeader());
                HttpResponse response = httpRequest.execute();
                String jsonStr = response.body();
                JSONArray jsonArray = JSON.parseArray(jsonStr);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (Config.getRedisExpireTime() != 0) {
                        jedis.sadd(groupId, (String) jsonObject.get("id"));
                    }
                }
            }
            Instant end = Instant.now();
            logger.info("访问率缓存已更新，耗时：" + Duration.between(start, end));
            if (Config.getRedisExpireTime() != 0) {
                int timeOffset = Config.getRedisExpireTime();
                LocalDateTime expireTime = LocalDateTime.now().plus(timeOffset, ChronoUnit.SECONDS);
                jedis.set("VisitStatisticsExpireTime", Config.dtf.format(expireTime));
            }
        }
    }

}
