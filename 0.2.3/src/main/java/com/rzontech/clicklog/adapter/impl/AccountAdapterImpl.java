package com.rzontech.clicklog.adapter.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rzontech.clicklog.adapter.AccountAdapter;
import com.rzontech.clicklog.adapter.AsyncProxy;
import com.rzontech.clicklog.config.Config;
import com.rzontech.clicklog.model.Group;
import com.rzontech.clicklog.util.RedisUtil;
import com.rzontech.clicklog.util.UAMSUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisDataException;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AccountAdapterImpl implements AccountAdapter {

    /**
     * 涉及到的接口
     * UAMS：
     * /users/userId/groups
     * /groups/groupId/users
     * /groups
     * /groups/groupId/offspring
     * Scenator:
     * /permissions/scenes/groupId
     * /namespaces
     */


    @Resource
    AsyncProxy asyncProxy;

    /**
     * 从Scenator获取所有场景，从UAMS获取所有用户组。
     * 遍历场景；对应每一个场景，遍历用户组；对应每一个用户组，获取其场景权限，如果场景权限与遍历中的场景对应，则从UAMS获取用户组所有用户并加入Set中去重。
     * 最终每一个场景对应的Set元素数量就是拥有对应场景权限的用户数量。
     * 系统管理组默认拥有所有权限，无法通过Scenator获取其拥有的权限。
     */
    //todo 获取场景时通过获取所有用户组拥有权限的场景，还是通过接口获取所有场景。循环Group获取所有用户组对应的用户、拥有的场景权限，可以在此次循环中获取所有场景
    @Override
    public Map<String, Integer> getAccountNum(String cookie) {
        List<Group> groups = getAllGroups();
        JSONArray contentJson = getAllContentJsonArray(cookie);
        List<String> allGroup = convertFromJsonArray(contentJson);
        Map<String, Integer> userNumByContentMap = allGroup.stream().collect(Collectors.toMap(String::toString, str -> 0));  //存储key为场景，value为用户数量
        Map<String, List<String>> contentByGroupMap = new HashMap<>(); //存储key为用户组id，value为用户组拥有的场景权限
        Map<String, Set<String>> usersByGroupMap = new HashMap<>(); //存储key为用户组id，value为用户组对应所有用户id。
        try (Jedis jedis = RedisUtil.getRedisConn()){
            for (Group group : groups) {
                Set<String> user = new HashSet<>(); //user存储每个用户组对应的所有用户Id
                String groupId = group.getId();
                getUsersIdByGroup(jedis, groupId, user);
                usersByGroupMap.put(groupId, user);
                List<String> contentByGroup = getContentByGroup(cookie, groupId); //一个用户组拥有的场景权限
                if (contentByGroup == null) {
                    contentByGroupMap.put(groupId, new ArrayList<>());
                } else {
                    contentByGroupMap.put(groupId, contentByGroup);
                }
            }
        }
        for (Map.Entry<String, Integer> entry : userNumByContentMap.entrySet()) {
            Set<String> distinctUser = new HashSet<>();
            for (Group group : groups) {
                String groupId = group.getId();
                List<String> contentByGroup;
                if (group.getName().equals("系统管理组")) {
                    contentByGroup = allGroup;
                } else {
                    contentByGroup = contentByGroupMap.get(groupId);
                    if (contentByGroup == null || contentByGroup.isEmpty()) {
                        continue;
                    }
                }
                if (contentByGroup.contains(entry.getKey())) {
                    distinctUser.addAll(usersByGroupMap.get(groupId));
                }
            }
            userNumByContentMap.put(entry.getKey(), distinctUser.size());
        }
        if (isStatisticsExpired()) {
            asyncProxy.updateRedisUsersId(groups);
        }
        return userNumByContentMap;
    }

    /**
     * 从UAMS获取groupId对应用户组及其所有子用户组的用户数量。
     * 通过Set进行用户去重。
     * 二级机构无法选择”系统管理组“，所以无需处理。
     */
    @Override
    public Map<String, Integer> getAccountNum(String cookie, String groupId) {
        JSONArray contentJson = getAllContentJsonArray(cookie);
        List<String> allGroup = convertFromJsonArray(contentJson);
        Map<String, Integer> result = allGroup.stream().collect(Collectors.toMap(String::toString, str -> 0));
        Map<String, Set<String>> contentUserMap = result.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, (entry) -> new HashSet<>()));
        Instant start = Instant.now();
        try (Jedis jedis = RedisUtil.getRedisConn()) {
            for (Map.Entry<String, Integer> entry : result.entrySet()) {
                List<String> groupIds = addGroupOffspringIdIfExists(groupId);
                for (String id : groupIds) {
                    List<String> contentByGroup = getContentByGroup(cookie, id);
                    if (contentByGroup == null)
                        continue;
                    if (contentByGroup.contains(entry.getKey())) {
                        Set<String> existUsers = contentUserMap.get(entry.getKey());
                        getUsersIdByGroup(jedis, id, existUsers);
                    }
                }
            }
        }
        result = contentUserMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().size()));
        if (isStatisticsExpired()) {
            asyncProxy.updateRedisUsersId(getAllGroups());
        }
        Instant end = Instant.now();
        System.out.println("从UAMS或Redis获取数据耗时：" + Duration.between(start, end));
        return result;
    }

    /**
     * 从UAMS根据用户ID获取其所属的用户组
     */
    @Override
    public List<Group> getGroupsByUser(String userId) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/users/" + userId + "/groups");
        return getGroupsFromUAMS(httpRequest);
    }

    /**
     * 查看用户组是否存在子用户组，如果存在，将其ID加入列表并返回
     *
     * @return 用户组ID列表
     */
    @Override
    public List<String> addGroupOffspringIdIfExists(String groupId) {
        List<Group> groups = getGroupOffspring(groupId);
        List<String> groupIds = new ArrayList<>();
        if (groups != null && groups.size() != 0) {
            groupIds = groups.stream().map(Group::getId).collect(Collectors.toList());
        }
        groupIds.add(groupId);
        return groupIds;
    }

    private void getUsersIdByGroup(Jedis jedis, String groupId, Set<String> usersId) throws JedisDataException {
        if (Config.getRedisExpireTime() == 0) {
            getUsersIdByGroupFromUams(groupId, usersId);
        } else {
            try {
                getUsersIdByGroupFromRedis(jedis, groupId, usersId);
            } catch (Exception e) {
                e.printStackTrace();
                getUsersIdByGroupFromUams(groupId, usersId);
            }
        }
    }


    private void getUsersIdByGroupFromRedis(Jedis jedis, String groupId, Set<String> usersId) throws JedisDataException {
        Set<String> usersIdByGroup = jedis.smembers(groupId);
        if (usersIdByGroup != null && !usersIdByGroup.isEmpty()) {
            usersId.addAll(usersIdByGroup);
        }
    }

    /**
     * 根据用户组ID获取用户，因为存在用户从属三级机构时，同样从属于其父级机构的情况。
     * 为了不重复计算，需要加入到usersId的Set中去重
     */
    private void getUsersIdByGroupFromUams(String groupId, Set<String> usersId) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/groups/" + groupId + "/users");
        httpRequest.addHeaders(new UAMSUtil().getUamsHeader());
        HttpResponse response = httpRequest.execute();
        String jsonStr = response.body();
        JSONArray jsonArray = JSON.parseArray(jsonStr);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            usersId.add((String) jsonObject.get("id"));
        }
    }

    /**
     * 从UAMS获取所有用户组
     */
    @Override
    public List<Group> getAllGroups() {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/groups");
        return getGroupsFromUAMS(httpRequest);
    }

    @Override
    public Group getGroup(String groupId) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/groups/" + groupId);
        httpRequest.addHeaders(new UAMSUtil().getUamsHeader());
        HttpResponse response = httpRequest.execute();
        String jsonStr = response.body();
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        if ("".equals(jsonStr) || jsonStr == null || jsonStr.isEmpty())
            return null;
        if (jsonStr.contains("RESOURCE_NOT_FOUND"))
            return null;
        return JSONObject.toJavaObject(jsonObject, Group.class);
    }

    /**
     * 从Scenator获取groupId对应用户组拥有的场景权限
     */
    private List<String> getContentByGroup(String cookie, String groupId) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getScenatorUrl() + "/permissions/scenes/" + groupId);
        JSONArray jsonArray = getContentsFromScenator(cookie, httpRequest, "sceneNameList");
        if (jsonArray == null)
            return null;
        return convertFromJsonArray(jsonArray);
    }

    /**
     * 从Scenator获取所有场景
     */
    private JSONArray getAllContentJsonArray(String cookie) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getScenatorUrl() + "/namespaces");
        return getContentsFromScenator(cookie, httpRequest, "scenes");
    }

    /**
     * 根据不同请求从UAMS获取用户组列表
     */
    private List<Group> getGroupsFromUAMS(HttpRequest httpRequest) {
        httpRequest.addHeaders(new UAMSUtil().getUamsHeader());
        HttpResponse response = httpRequest.execute();
        String jsonStr = response.body();
        if ("".equals(jsonStr) || jsonStr == null || jsonStr.isEmpty())
            return null;
        if (jsonStr.contains("RESOURCE_NOT_FOUND"))
            return null;
        JSONArray jsonArray = JSON.parseArray(jsonStr);
        return jsonArray.toJavaList(Group.class);
    }

    /**
     * 根据不同请求从Scenator获取场景
     */
    private JSONArray getContentsFromScenator(String cookie, HttpRequest httpRequest, String contentKeyName) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        httpRequest.addHeaders(headers);
        HttpResponse response = httpRequest.execute();
        String jsonStr = response.body();
        if ("".equals(jsonStr) || jsonStr == null || jsonStr.isEmpty())
            return null;
        if (jsonStr.contains("RESOURCE_NOT_FOUND"))
            return null;
        return JSON.parseObject(jsonStr).getJSONArray(contentKeyName);
    }

    private List<Group> getGroupOffspring(String groupId) {
        HttpRequest httpRequest = HttpUtil.createGet(Config.getUamsUrl() + "/groups/" + groupId + "/offspring");
        return getGroupsFromUAMS(httpRequest);
    }

    private List<String> convertFromJsonArray(JSONArray jsonArray) {
        return jsonArray.toJavaList(String.class);
    }

    private boolean isStatisticsExpired() {
        try (Jedis jedis = RedisUtil.getRedisConn()) {
            String timeStr = jedis.get("VisitStatisticsExpireTime");
            if (StringUtils.isEmpty(timeStr))
                return true;
            LocalDateTime expireTime = LocalDateTime.parse(timeStr, Config.dtf);
            return LocalDateTime.now().isAfter(expireTime);
        }
    }

}