package com.rzontech.clicklog.adapter;

import com.rzontech.clicklog.model.Group;

import java.util.List;
import java.util.Map;

public interface AccountAdapter {

    Map<String, Integer> getAccountNum(String cookie);

    Map<String, Integer> getAccountNum(String cookie, String groupId);

    List<Group> getGroupsByUser(String userId);

    List<String> addGroupOffspringIdIfExists(String groupId);

    List<Group> getAllGroups();

    Group getGroup(String id);

}
